/**
 * Sessions Management Overlay
 * 
 * Browse, load, and manage saved conversation sessions
 * Supports session preview, deletion, and restoration
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Box, Text, useInput } from 'ink';
import { getSavedSessions, loadSession, deleteSession } from '../../utils/storage/save-rollout.js';
import type { SessionMetadata, SessionData } from '../../types/index.js';

interface SessionsOverlayProps {
  onLoadSession: (sessionData: SessionData) => void;
  onClose: () => void;
  visible: boolean;
}

type ViewMode = 'list' | 'preview' | 'confirm-delete';

export function SessionsOverlay({
  onLoadSession,
  onClose,
  visible
}: SessionsOverlayProps) {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [sessions, setSessions] = useState<SessionMetadata[]>([]);
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [previewData, setPreviewData] = useState<SessionData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load sessions when overlay becomes visible
  useEffect(() => {
    if (visible) {
      loadSessions();
    }
  }, [visible]);

  /**
   * Load available sessions
   */
  const loadSessions = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const sessionList = await getSavedSessions();
      setSessions(sessionList.sort((a, b) => b.lastActivity - a.lastActivity)); // Sort by most recent
      setSelectedIndex(0);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load sessions');
      setSessions([]);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Load session preview
   */
  const loadPreview = useCallback(async (sessionId: string) => {
    setLoading(true);
    setError(null);

    try {
      const sessionData = await loadSession(sessionId);
      setPreviewData(sessionData);
      setViewMode('preview');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load session preview');
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Delete session
   */
  const handleDeleteSession = useCallback(async (sessionId: string) => {
    setLoading(true);
    setError(null);

    try {
      await deleteSession(sessionId);
      await loadSessions(); // Refresh list
      setViewMode('list');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete session');
    } finally {
      setLoading(false);
    }
  }, [loadSessions]);

  // Handle keyboard input
  useInput((input, key) => {
    if (!visible) {return;}

    // Close overlay
    if (key.escape) {
      if (viewMode !== 'list') {
        setViewMode('list');
        setPreviewData(null);
        setError(null);
      } else {
        onClose();
      }
      return;
    }

    if (viewMode === 'list') {
      // Navigation
      if (key.upArrow) {
        setSelectedIndex(Math.max(0, selectedIndex - 1));
        return;
      }

      if (key.downArrow) {
        setSelectedIndex(Math.min(sessions.length - 1, selectedIndex + 1));
        return;
      }

      if (key.pageUp) {
        setSelectedIndex(Math.max(0, selectedIndex - 10));
        return;
      }

      if (key.pageDown) {
        setSelectedIndex(Math.min(sessions.length - 1, selectedIndex + 10));
        return;
      }

      // Actions
      if (key.return) {
        const selectedSession = sessions[selectedIndex];
        if (selectedSession) {
          loadPreview(selectedSession.id);
        }
        return;
      }

      if (input === 'l' || input === 'L') {
        const selectedSession = sessions[selectedIndex];
        if (selectedSession) {
          loadPreview(selectedSession.id);
        }
        return;
      }

      if (input === 'd' || input === 'D') {
        if (sessions[selectedIndex]) {
          setViewMode('confirm-delete');
        }
        return;
      }

      if (input === 'r' || input === 'R') {
        loadSessions();
        return;
      }
    } else if (viewMode === 'preview') {
      if (key.return) {
        if (previewData) {
          onLoadSession(previewData);
          onClose();
        }
        return;
      }
    } else if (viewMode === 'confirm-delete') {
      if (input === 'y' || input === 'Y') {
        const selectedSession = sessions[selectedIndex];
        if (selectedSession) {
          handleDeleteSession(selectedSession.id);
        }
        return;
      }

      if (input === 'n' || input === 'N') {
        setViewMode('list');
        return;
      }
    }
  });

  /**
   * Format timestamp for display
   */
  const formatTimestamp = useCallback((timestamp: number): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffHours < 1) {
      return 'Just now';
    } else if (diffHours < 24) {
      return `${diffHours}h ago`;
    } else if (diffDays < 7) {
      return `${diffDays}d ago`;
    } else {
      return date.toLocaleDateString();
    }
  }, []);

  /**
   * Format session duration
   */
  const formatDuration = useCallback((start: number, end: number): string => {
    const durationMs = end - start;
    const minutes = Math.floor(durationMs / (1000 * 60));
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else {
      return `${minutes}m`;
    }
  }, []);

  if (!visible) {
    return null;
  }

  return (
    <Box
      position="absolute"
      borderStyle="double"
      borderColor="cyan"
      flexDirection="column"
    >
      {/* Header */}
      <Box paddingX={2} paddingY={1} borderStyle="single" borderColor="gray">
        <Text color="cyan" bold>
          Session Management
        </Text>
        <Box>
          <Text color="gray">
            {viewMode === 'list' && `${sessions.length} sessions available`}
            {viewMode === 'preview' && 'Session Preview'}
            {viewMode === 'confirm-delete' && 'Confirm Deletion'}
          </Text>
        </Box>
      </Box>

      {/* Content */}
      <Box flexGrow={1} paddingX={2} paddingY={1}>
        {loading ? (
          <Text color="yellow">Loading...</Text>
        ) : error ? (
          <Text color="red">{error}</Text>
        ) : viewMode === 'list' ? (
          <Box flexDirection="column">
            {sessions.length === 0 ? (
              <Text color="gray">No saved sessions found.</Text>
            ) : (
              sessions.map((session, index) => {
                const isSelected = index === selectedIndex;
                const duration = formatDuration(session.timestamp, session.lastActivity);

                return (
                  <Box key={session.id}>
                    <Box width={3}>
                      <Text color={isSelected ? "black" : "gray"} backgroundColor={isSelected ? "cyan" : undefined}>
                        {isSelected ? '►' : ' '}
                      </Text>
                    </Box>
                    <Box width={12}>
                      <Text color={isSelected ? "black" : "gray"} backgroundColor={isSelected ? "cyan" : undefined}>
                        {formatTimestamp(session.lastActivity)}
                      </Text>
                    </Box>
                    <Box width={15}>
                      <Text color={isSelected ? "black" : "blue"} backgroundColor={isSelected ? "cyan" : undefined}>
                        {session.provider}/{session.model}
                      </Text>
                    </Box>
                    <Box width={8}>
                      <Text color={isSelected ? "black" : "gray"} backgroundColor={isSelected ? "cyan" : undefined}>
                        {session.itemCount} msgs
                      </Text>
                    </Box>
                    <Box width={8}>
                      <Text color={isSelected ? "black" : "gray"} backgroundColor={isSelected ? "cyan" : undefined}>
                        {duration}
                      </Text>
                    </Box>
                    <Box flexGrow={1}>
                      <Text
                        color={isSelected ? "black" : "white"}
                        backgroundColor={isSelected ? "cyan" : undefined}
                        bold={isSelected}
                      >
                        {session.id.substring(0, 8)}...
                      </Text>
                    </Box>
                  </Box>
                );
              })
            )}
          </Box>
        ) : viewMode === 'preview' ? (
          <Box flexDirection="column">
            {previewData && (
              <>
                <Text color="blue" bold>
                  Session Details:
                </Text>
                <Text>ID: {previewData.metadata.id}</Text>
                <Text>Provider: {previewData.metadata.provider}</Text>
                <Text>Model: {previewData.metadata.model}</Text>
                <Text>Messages: {previewData.metadata.itemCount}</Text>
                <Text>Created: {new Date(previewData.metadata.timestamp).toLocaleString()}</Text>
                <Text>Last Activity: {new Date(previewData.metadata.lastActivity).toLocaleString()}</Text>
                
                <Text color="blue" bold>
                  Recent Messages:
                </Text>
                {previewData.items.slice(-3).map((item, index) => (
                  <Box key={index}>
                    <Text color="gray">
                      {item.role === 'user' ? '👤' : '🤖'} {item.type}:
                    </Text>
                    <Text>
                      {item.type === 'message' 
                        ? item.content.map(c => c.type === 'input_text' ? c.text : '[Image]').join(' ')
                        : item.type === 'output' 
                        ? (item as any).content 
                        : `${(item as any).name || 'Tool'}(${(item as any).arguments || (item as any).result || ''})`
                      }
                    </Text>
                  </Box>
                ))}
              </>
            )}
          </Box>
        ) : viewMode === 'confirm-delete' ? (
          <Box flexDirection="column">
            <Text color="red" bold>
              Delete Session?
            </Text>
            <Text>
              Are you sure you want to delete this session?
            </Text>
            <Text color="gray">
              Session: {sessions[selectedIndex]?.id.substring(0, 16)}...
            </Text>
            <Text color="yellow">
              This action cannot be undone.
            </Text>
          </Box>
        ) : null}
      </Box>

      {/* Footer */}
      <Box paddingX={2} paddingY={1} borderStyle="single" borderColor="gray">
        <Text color="gray">
          {viewMode === 'list' && 'Enter/L: Preview • D: Delete • R: Refresh • Esc: Close'}
          {viewMode === 'preview' && 'Enter: Load Session • Esc: Back'}
          {viewMode === 'confirm-delete' && 'Y: Confirm • N: Cancel'}
        </Text>
      </Box>
    </Box>
  );
}
