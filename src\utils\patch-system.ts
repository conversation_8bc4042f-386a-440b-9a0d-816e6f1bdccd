/**
 * Advanced Patch System
 * 
 * Provides intelligent patch generation, application, and management
 * Supports unified diff format, conflict resolution, and rollback
 */

import { promises as fs } from 'fs';
import { join, basename } from 'path';
import { createHash } from 'crypto';
import { logInfo, logError, logWarn } from './logger/log.js';

export interface PatchOperation {
  type: 'add' | 'remove' | 'modify';
  file: string;
  lineNumber: number;
  content: string;
  originalContent?: string;
}

export interface Patch {
  id: string;
  timestamp: string;
  description: string;
  operations: PatchOperation[];
  checksum: string;
  metadata: {
    author?: string;
    version?: string;
    dependencies?: string[];
  };
}

export interface PatchResult {
  success: boolean;
  appliedOperations: number;
  failedOperations: PatchOperation[];
  conflicts: PatchConflict[];
  warnings: string[];
}

export interface PatchConflict {
  file: string;
  lineNumber: number;
  expected: string;
  actual: string;
  operation: PatchOperation;
}

/**
 * Patch manager for handling file modifications
 */
export class PatchManager {
  private appliedPatches = new Map<string, Patch>();
  private backupDirectory: string;

  constructor(backupDirectory: string = '.patches') {
    this.backupDirectory = backupDirectory;
  }

  /**
   * Generate patch from file differences
   */
  async generatePatch(
    originalFile: string,
    modifiedFile: string,
    description: string = 'Auto-generated patch'
  ): Promise<Patch> {
    try {
      const originalContent = await fs.readFile(originalFile, 'utf-8');
      const modifiedContent = await fs.readFile(modifiedFile, 'utf-8');

      const operations = this.diffToOperations(originalContent, modifiedContent, originalFile);
      const checksum = this.calculateChecksum(operations);

      const patch: Patch = {
        id: this.generatePatchId(),
        timestamp: new Date().toISOString(),
        description,
        operations,
        checksum,
        metadata: {}
      };

      logInfo(`Generated patch ${patch.id} with ${operations.length} operations`);
      return patch;

    } catch (error) {
      logError('Failed to generate patch', error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }

  /**
   * Apply patch to files
   */
  async applyPatch(patch: Patch, dryRun: boolean = false): Promise<PatchResult> {
    const result: PatchResult = {
      success: true,
      appliedOperations: 0,
      failedOperations: [],
      conflicts: [],
      warnings: []
    };

    // Validate patch checksum
    const calculatedChecksum = this.calculateChecksum(patch.operations);
    if (calculatedChecksum !== patch.checksum) {
      result.warnings.push('Patch checksum mismatch - patch may be corrupted');
    }

    // Create backup directory if not dry run
    if (!dryRun) {
      await this.ensureBackupDirectory();
    }

    // Apply each operation
    for (const operation of patch.operations) {
      try {
        const operationResult = await this.applyOperation(operation, dryRun);
        
        if (operationResult.success) {
          result.appliedOperations++;
        } else {
          result.failedOperations.push(operation);
          result.conflicts.push(...operationResult.conflicts);
          result.success = false;
        }

      } catch (error) {
        result.failedOperations.push(operation);
        result.success = false;
        logError(`Failed to apply operation on ${operation.file}`, error instanceof Error ? error : new Error(String(error)));
      }
    }

    // Store patch if successful and not dry run
    if (result.success && !dryRun) {
      this.appliedPatches.set(patch.id, patch);
      await this.savePatchRecord(patch);
    }

    logInfo(`Patch ${patch.id} applied: ${result.appliedOperations}/${patch.operations.length} operations successful`);
    return result;
  }

  /**
   * Apply single operation
   */
  private async applyOperation(
    operation: PatchOperation,
    dryRun: boolean
  ): Promise<{ success: boolean; conflicts: PatchConflict[] }> {
    const conflicts: PatchConflict[] = [];

    try {
      // Read current file content
      let currentContent = '';
      try {
        currentContent = await fs.readFile(operation.file, 'utf-8');
      } catch {
        if (operation.type !== 'add') {
          throw new Error(`File ${operation.file} does not exist`);
        }
      }

      const lines = currentContent.split('\n');

      switch (operation.type) {
        case 'add':
          if (!dryRun) {
            await this.backupFile(operation.file);
            lines.splice(operation.lineNumber, 0, operation.content);
            await fs.writeFile(operation.file, lines.join('\n'));
          }
          break;

        case 'remove':
          if (operation.lineNumber >= lines.length) {
            conflicts.push({
              file: operation.file,
              lineNumber: operation.lineNumber,
              expected: operation.originalContent || '',
              actual: 'EOF',
              operation
            });
            return { success: false, conflicts };
          }

          const lineToRemove = lines[operation.lineNumber];
          if (operation.originalContent && lineToRemove !== operation.originalContent) {
            conflicts.push({
              file: operation.file,
              lineNumber: operation.lineNumber,
              expected: operation.originalContent,
              actual: lineToRemove,
              operation
            });
            return { success: false, conflicts };
          }

          if (!dryRun) {
            await this.backupFile(operation.file);
            lines.splice(operation.lineNumber, 1);
            await fs.writeFile(operation.file, lines.join('\n'));
          }
          break;

        case 'modify':
          if (operation.lineNumber >= lines.length) {
            conflicts.push({
              file: operation.file,
              lineNumber: operation.lineNumber,
              expected: operation.originalContent || '',
              actual: 'EOF',
              operation
            });
            return { success: false, conflicts };
          }

          const lineToModify = lines[operation.lineNumber];
          if (operation.originalContent && lineToModify !== operation.originalContent) {
            conflicts.push({
              file: operation.file,
              lineNumber: operation.lineNumber,
              expected: operation.originalContent,
              actual: lineToModify,
              operation
            });
            return { success: false, conflicts };
          }

          if (!dryRun) {
            await this.backupFile(operation.file);
            lines[operation.lineNumber] = operation.content;
            await fs.writeFile(operation.file, lines.join('\n'));
          }
          break;
      }

      return { success: true, conflicts: [] };

    } catch (error) {
      logError(`Failed to apply ${operation.type} operation`, error instanceof Error ? error : new Error(String(error)));
      return { success: false, conflicts };
    }
  }

  /**
   * Rollback patch
   */
  async rollbackPatch(patchId: string): Promise<boolean> {
    const patch = this.appliedPatches.get(patchId);
    if (!patch) {
      throw new Error(`Patch ${patchId} not found`);
    }

    try {
      // Restore files from backup
      for (const operation of patch.operations) {
        await this.restoreFile(operation.file, patchId);
      }

      this.appliedPatches.delete(patchId);
      await this.removePatchRecord(patchId);

      logInfo(`Patch ${patchId} rolled back successfully`);
      return true;

    } catch (error) {
      logError(`Failed to rollback patch ${patchId}`, error instanceof Error ? error : new Error(String(error)));
      return false;
    }
  }

  /**
   * Convert diff to operations
   */
  private diffToOperations(original: string, modified: string, filePath: string): PatchOperation[] {
    const operations: PatchOperation[] = [];
    const originalLines = original.split('\n');
    const modifiedLines = modified.split('\n');

    // Simple line-by-line diff (could be enhanced with more sophisticated algorithms)
    const maxLines = Math.max(originalLines.length, modifiedLines.length);
    
    for (let i = 0; i < maxLines; i++) {
      const originalLine = originalLines[i];
      const modifiedLine = modifiedLines[i];

      if (originalLine === undefined && modifiedLine !== undefined) {
        // Line added
        operations.push({
          type: 'add',
          file: filePath,
          lineNumber: i,
          content: modifiedLine
        });
      } else if (originalLine !== undefined && modifiedLine === undefined) {
        // Line removed
        operations.push({
          type: 'remove',
          file: filePath,
          lineNumber: i,
          content: '',
          originalContent: originalLine
        });
      } else if (originalLine !== modifiedLine) {
        // Line modified
        operations.push({
          type: 'modify',
          file: filePath,
          lineNumber: i,
          content: modifiedLine,
          originalContent: originalLine
        });
      }
    }

    return operations;
  }

  /**
   * Calculate patch checksum
   */
  private calculateChecksum(operations: PatchOperation[]): string {
    const content = JSON.stringify(operations, null, 0);
    return createHash('sha256').update(content).digest('hex');
  }

  /**
   * Generate unique patch ID
   */
  private generatePatchId(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2);
    return `patch-${timestamp}-${random}`;
  }

  /**
   * Backup file before modification
   */
  private async backupFile(filePath: string): Promise<void> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const backupPath = join(this.backupDirectory, `${basename(filePath)}.backup`);
      await fs.writeFile(backupPath, content);
    } catch (error) {
      // File might not exist, which is okay for new files
      if ((error as any).code !== 'ENOENT') {
        throw error;
      }
    }
  }

  /**
   * Restore file from backup
   */
  private async restoreFile(filePath: string, _patchId: string): Promise<void> {
    const backupPath = join(this.backupDirectory, `${basename(filePath)}.backup`);
    
    try {
      const content = await fs.readFile(backupPath, 'utf-8');
      await fs.writeFile(filePath, content);
    } catch {
      logWarn(`Could not restore ${filePath} from backup`);
    }
  }

  /**
   * Ensure backup directory exists
   */
  private async ensureBackupDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.backupDirectory, { recursive: true });
    } catch {
      // Directory might already exist
    }
  }

  /**
   * Save patch record
   */
  private async savePatchRecord(patch: Patch): Promise<void> {
    const recordPath = join(this.backupDirectory, `${patch.id}.json`);
    await fs.writeFile(recordPath, JSON.stringify(patch, null, 2));
  }

  /**
   * Remove patch record
   */
  private async removePatchRecord(patchId: string): Promise<void> {
    const recordPath = join(this.backupDirectory, `${patchId}.json`);
    try {
      await fs.unlink(recordPath);
    } catch {
      // File might not exist
    }
  }
}

// Global patch manager instance
export const patchManager = new PatchManager();
