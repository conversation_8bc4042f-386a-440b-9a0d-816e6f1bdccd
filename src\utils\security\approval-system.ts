/**
 * Security & Approval System
 * 
 * Provides comprehensive security controls and approval workflows
 * Includes risk assessment, user confirmation, and audit logging
 */

import { promises as fs } from 'fs';
import { join } from 'path';
import { logInfo, logWarn, logError } from '../logger/log.js';

export interface SecurityAction {
  id: string;
  type: 'file_write' | 'file_delete' | 'command_execute' | 'network_request' | 'system_modify';
  description: string;
  target: string;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  metadata: Record<string, any>;
  timestamp: string;
}

export interface ApprovalRequest {
  id: string;
  action: SecurityAction;
  requiredApprovals: number;
  approvals: Approval[];
  status: 'pending' | 'approved' | 'rejected' | 'expired';
  expiresAt: string;
  createdAt: string;
}

export interface Approval {
  userId: string;
  decision: 'approve' | 'reject';
  reason?: string;
  timestamp: string;
}

export interface SecurityPolicy {
  autoApprove: {
    lowRisk: boolean;
    mediumRisk: boolean;
    highRisk: boolean;
    criticalRisk: boolean;
  };
  requiredApprovals: {
    low: number;
    medium: number;
    high: number;
    critical: number;
  };
  approvalTimeout: number; // minutes
  auditLogging: boolean;
  restrictedPaths: string[];
  allowedCommands: string[];
  blockedCommands: string[];
}

/**
 * Security and approval manager
 */
export class ApprovalManager {
  private pendingRequests = new Map<string, ApprovalRequest>();
  private auditLog: SecurityAction[] = [];
  private policy: SecurityPolicy;

  constructor(policy?: Partial<SecurityPolicy>) {
    this.policy = {
      autoApprove: {
        lowRisk: true,
        mediumRisk: false,
        highRisk: false,
        criticalRisk: false
      },
      requiredApprovals: {
        low: 0,
        medium: 1,
        high: 2,
        critical: 3
      },
      approvalTimeout: 30,
      auditLogging: true,
      restrictedPaths: [
        '/etc',
        '/usr/bin',
        '/usr/sbin',
        '/bin',
        '/sbin',
        'C:\\Windows',
        'C:\\Program Files'
      ],
      allowedCommands: [
        'ls', 'dir', 'cat', 'type', 'echo', 'pwd', 'cd',
        'git', 'npm', 'yarn', 'node', 'python', 'pip'
      ],
      blockedCommands: [
        'rm', 'del', 'format', 'fdisk', 'mkfs',
        'sudo', 'su', 'chmod', 'chown'
      ],
      ...policy
    };
  }

  /**
   * Request approval for security action
   */
  async requestApproval(action: SecurityAction): Promise<ApprovalRequest> {
    const requiredApprovals = this.policy.requiredApprovals[action.riskLevel];
    const expiresAt = new Date(Date.now() + this.policy.approvalTimeout * 60 * 1000).toISOString();

    const request: ApprovalRequest = {
      id: this.generateRequestId(),
      action,
      requiredApprovals,
      approvals: [],
      status: 'pending',
      expiresAt,
      createdAt: new Date().toISOString()
    };

    // Check if auto-approval is enabled for this risk level
    if (this.shouldAutoApprove(action.riskLevel)) {
      request.status = 'approved';
      request.approvals.push({
        userId: 'system',
        decision: 'approve',
        reason: 'Auto-approved based on policy',
        timestamp: new Date().toISOString()
      });
    } else {
      this.pendingRequests.set(request.id, request);
    }

    // Log the action
    if (this.policy.auditLogging) {
      this.auditLog.push(action);
      await this.saveAuditLog();
    }

    logInfo(`Approval request ${request.id} created for ${action.type}: ${action.description}`);
    return request;
  }

  /**
   * Provide approval for a request
   */
  async provideApproval(
    requestId: string,
    userId: string,
    decision: 'approve' | 'reject',
    reason?: string
  ): Promise<boolean> {
    const request = this.pendingRequests.get(requestId);
    if (!request) {
      throw new Error(`Approval request ${requestId} not found`);
    }

    if (request.status !== 'pending') {
      throw new Error(`Request ${requestId} is no longer pending`);
    }

    if (new Date() > new Date(request.expiresAt)) {
      request.status = 'expired';
      this.pendingRequests.delete(requestId);
      throw new Error(`Request ${requestId} has expired`);
    }

    // Check if user already provided approval
    const existingApproval = request.approvals.find(a => a.userId === userId);
    if (existingApproval) {
      throw new Error(`User ${userId} has already provided approval for request ${requestId}`);
    }

    // Add approval
    const approval: Approval = {
      userId,
      decision,
      reason,
      timestamp: new Date().toISOString()
    };

    request.approvals.push(approval);

    // Check if request is now approved or rejected
    const approvals = request.approvals.filter(a => a.decision === 'approve').length;
    const rejections = request.approvals.filter(a => a.decision === 'reject').length;

    if (rejections > 0) {
      request.status = 'rejected';
      this.pendingRequests.delete(requestId);
    } else if (approvals >= request.requiredApprovals) {
      request.status = 'approved';
      this.pendingRequests.delete(requestId);
    }

    logInfo(`Approval provided for request ${requestId}: ${decision} by ${userId}`);
    return request.status === 'approved';
  }

  /**
   * Assess risk level for an action
   */
  assessRisk(action: Omit<SecurityAction, 'id' | 'riskLevel' | 'timestamp'>): 'low' | 'medium' | 'high' | 'critical' {
    // File operations
    if (action.type === 'file_delete') {
      if (this.isRestrictedPath(action.target)) {
        return 'critical';
      }
      return 'medium';
    }

    if (action.type === 'file_write') {
      if (this.isRestrictedPath(action.target)) {
        return 'high';
      }
      if (action.target.includes('config') || action.target.includes('.env')) {
        return 'medium';
      }
      return 'low';
    }

    // Command execution
    if (action.type === 'command_execute') {
      const command = action.target.split(' ')[0];
      
      if (this.policy.blockedCommands.includes(command)) {
        return 'critical';
      }
      
      if (this.policy.allowedCommands.includes(command)) {
        return 'low';
      }
      
      return 'medium';
    }

    // Network requests
    if (action.type === 'network_request') {
      if (action.target.includes('localhost') || action.target.includes('127.0.0.1')) {
        return 'low';
      }
      return 'medium';
    }

    // System modifications
    if (action.type === 'system_modify') {
      return 'high';
    }

    return 'medium';
  }

  /**
   * Create security action with risk assessment
   */
  createSecurityAction(
    type: SecurityAction['type'],
    description: string,
    target: string,
    metadata: Record<string, any> = {}
  ): SecurityAction {
    const action: Omit<SecurityAction, 'id' | 'riskLevel' | 'timestamp'> = {
      type,
      description,
      target,
      metadata
    };

    const riskLevel = this.assessRisk(action);

    return {
      ...action,
      id: this.generateActionId(),
      riskLevel,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Check if path is restricted
   */
  private isRestrictedPath(path: string): boolean {
    return this.policy.restrictedPaths.some(restricted => 
      path.startsWith(restricted)
    );
  }

  /**
   * Check if action should be auto-approved
   */
  private shouldAutoApprove(riskLevel: 'low' | 'medium' | 'high' | 'critical'): boolean {
    switch (riskLevel) {
      case 'low': return this.policy.autoApprove.lowRisk;
      case 'medium': return this.policy.autoApprove.mediumRisk;
      case 'high': return this.policy.autoApprove.highRisk;
      case 'critical': return this.policy.autoApprove.criticalRisk;
      default: return false;
    }
  }

  /**
   * Generate unique request ID
   */
  private generateRequestId(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2);
    return `req-${timestamp}-${random}`;
  }

  /**
   * Generate unique action ID
   */
  private generateActionId(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2);
    return `act-${timestamp}-${random}`;
  }

  /**
   * Save audit log to file
   */
  private async saveAuditLog(): Promise<void> {
    try {
      const logPath = join(process.cwd(), '.security-audit.json');
      await fs.writeFile(logPath, JSON.stringify(this.auditLog, null, 2));
    } catch (error) {
      logError('Failed to save audit log', error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * Get pending requests
   */
  getPendingRequests(): ApprovalRequest[] {
    return Array.from(this.pendingRequests.values());
  }

  /**
   * Get audit log
   */
  getAuditLog(): SecurityAction[] {
    return [...this.auditLog];
  }

  /**
   * Update security policy
   */
  updatePolicy(updates: Partial<SecurityPolicy>): void {
    this.policy = { ...this.policy, ...updates };
    logInfo('Security policy updated');
  }

  /**
   * Get current policy
   */
  getPolicy(): SecurityPolicy {
    return { ...this.policy };
  }

  /**
   * Clean up expired requests
   */
  cleanupExpiredRequests(): void {
    const now = new Date();
    for (const [id, request] of this.pendingRequests) {
      if (new Date(request.expiresAt) < now) {
        request.status = 'expired';
        this.pendingRequests.delete(id);
        logWarn(`Request ${id} expired and was removed`);
      }
    }
  }
}

// Global approval manager instance
export const approvalManager = new ApprovalManager();

/**
 * Request approval for file operation
 */
export async function requestFileApproval(
  operation: 'read' | 'write' | 'delete',
  filePath: string,
  description?: string
): Promise<ApprovalRequest> {
  const actionType = operation === 'delete' ? 'file_delete' : 'file_write';
  const actionDescription = description || `${operation} file: ${filePath}`;

  const action = approvalManager.createSecurityAction(
    actionType,
    actionDescription,
    filePath,
    { operation }
  );

  return approvalManager.requestApproval(action);
}

/**
 * Request approval for command execution
 */
export async function requestCommandApproval(
  command: string,
  args: string[] = [],
  description?: string
): Promise<ApprovalRequest> {
  const fullCommand = `${command} ${args.join(' ')}`.trim();
  const actionDescription = description || `Execute command: ${fullCommand}`;

  const action = approvalManager.createSecurityAction(
    'command_execute',
    actionDescription,
    fullCommand,
    { command, args }
  );

  return approvalManager.requestApproval(action);
}
