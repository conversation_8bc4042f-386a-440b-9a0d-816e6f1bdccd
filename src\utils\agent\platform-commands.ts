/**
 * Platform Command Adaptation System
 * 
 * Converts Unix commands to Windows equivalents and handles
 * platform-specific command variations
 */

/**
 * Command mapping from Unix to Windows
 */
const COMMAND_MAP: Record<string, string> = {
  // File operations
  'ls': 'dir',
  'cat': 'type',
  'rm': 'del',
  'mv': 'move',
  'cp': 'copy',
  'mkdir': 'md',
  'rmdir': 'rd',
  'touch': 'echo.',
  'pwd': 'cd',
  'which': 'where',
  
  // Text processing
  'grep': 'findstr',
  'head': 'more',
  'tail': 'more',
  'wc': 'find /c /v ""',
  
  // Process management
  'ps': 'tasklist',
  'kill': 'taskkill',
  'killall': 'taskkill',
  
  // Network
  'curl': 'curl', // Available on modern Windows
  'wget': 'curl', // Use curl as wget alternative
  
  // Archive
  'tar': 'tar', // Available on modern Windows
  'unzip': 'tar', // Use tar for extraction
  
  // System info
  'uname': 'ver',
  'whoami': 'whoami',
  'id': 'whoami',
  'date': 'date',
  'uptime': 'systeminfo'
};

/**
 * Argument mapping for converted commands
 */
const ARGUMENT_MAP: Record<string, (args: string[]) => string[]> = {
  'dir': (args) => {
    // Convert ls arguments to dir arguments
    const dirArgs: string[] = [];
    
    for (const arg of args) {
      switch (arg) {
        case '-l':
        case '-la':
        case '-al':
          dirArgs.push('/Q'); // Show ownership
          break;
        case '-a':
          dirArgs.push('/A'); // Show hidden files
          break;
        case '-h':
          // Human readable - no direct equivalent
          break;
        case '-R':
        case '-r':
          dirArgs.push('/S'); // Recursive
          break;
        case '-t':
          dirArgs.push('/O:D'); // Sort by date
          break;
        case '-S':
          dirArgs.push('/O:S'); // Sort by size
          break;
        default:
          if (!arg.startsWith('-')) {
            dirArgs.push(arg);
          }
      }
    }
    
    return dirArgs;
  },
  
  'type': (args) => {
    // cat arguments - mostly direct mapping
    return args.filter(arg => !arg.startsWith('-'));
  },
  
  'del': (args) => {
    // rm arguments to del arguments
    const delArgs: string[] = [];
    
    for (const arg of args) {
      switch (arg) {
        case '-r':
        case '-rf':
        case '-R':
          delArgs.push('/S'); // Recursive
          delArgs.push('/Q'); // Quiet
          break;
        case '-f':
          delArgs.push('/Q'); // Quiet (force)
          break;
        case '-i':
          delArgs.push('/P'); // Prompt
          break;
        default:
          if (!arg.startsWith('-')) {
            delArgs.push(arg);
          }
      }
    }
    
    return delArgs;
  },
  
  'findstr': (args) => {
    // grep arguments to findstr arguments
    const findstrArgs: string[] = [];
    let pattern = '';
    const files: string[] = [];
    const _inFiles = false;
    
    for (let i = 0; i < args.length; i++) {
      const arg = args[i];
      
      switch (arg) {
        case '-i':
          findstrArgs.push('/I'); // Case insensitive
          break;
        case '-r':
        case '-E':
          findstrArgs.push('/R'); // Regular expressions
          break;
        case '-n':
          findstrArgs.push('/N'); // Line numbers
          break;
        case '-v':
          findstrArgs.push('/V'); // Invert match
          break;
        case '-l':
          findstrArgs.push('/M'); // Files with matches
          break;
        case '-c':
          findstrArgs.push('/C'); // Count matches
          break;
        default:
          if (!arg.startsWith('-')) {
            if (!pattern) {
              pattern = arg;
            } else {
              files.push(arg);
            }
          }
      }
    }
    
    if (pattern) {
      findstrArgs.push(pattern);
    }
    
    if (files.length > 0) {
      findstrArgs.push(...files);
    }
    
    return findstrArgs;
  },
  
  'tasklist': (args) => {
    // ps arguments to tasklist arguments
    const tasklistArgs: string[] = [];
    
    for (const arg of args) {
      switch (arg) {
        case '-e':
        case '-A':
          // Show all processes (default behavior)
          break;
        case '-f':
          tasklistArgs.push('/FO', 'LIST'); // Full format
          break;
        case '-u':
          tasklistArgs.push('/FO', 'CSV'); // User format
          break;
        default:
          if (!arg.startsWith('-')) {
            tasklistArgs.push(arg);
          }
      }
    }
    
    return tasklistArgs;
  },
  
  'taskkill': (args) => {
    // kill arguments to taskkill arguments
    const taskkillArgs: string[] = [];
    
    for (let i = 0; i < args.length; i++) {
      const arg = args[i];
      
      switch (arg) {
        case '-9':
          taskkillArgs.push('/F'); // Force
          break;
        case '-TERM':
        case '-15':
          // Default behavior
          break;
        default:
          if (!arg.startsWith('-')) {
            // Assume it's a PID
            taskkillArgs.push('/PID', arg);
          }
      }
    }
    
    return taskkillArgs;
  }
};

/**
 * Adapt command for current platform
 */
export function adaptCommandForPlatform(command: string[]): string[] {
  if (process.platform !== 'win32') {
    // On Unix-like systems, return command as-is
    return [...command];
  }
  
  // Windows adaptations
  const [cmd, ...args] = command;
  const lowerCmd = cmd.toLowerCase();
  
  // Check if command needs mapping
  const mappedCmd = COMMAND_MAP[lowerCmd];
  if (!mappedCmd) {
    // No mapping needed, return original
    return [...command];
  }
  
  // Apply argument mapping if available
  const argMapper = ARGUMENT_MAP[mappedCmd];
  const mappedArgs = argMapper ? argMapper(args) : args;
  
  return [mappedCmd, ...mappedArgs];
}

/**
 * Check if command is available on current platform
 */
export function isCommandAvailable(command: string): boolean {
  const lowerCmd = command.toLowerCase();
  
  if (process.platform === 'win32') {
    // Windows built-in commands
    const windowsCommands = [
      'dir', 'type', 'del', 'move', 'copy', 'md', 'rd', 'cd', 'where',
      'findstr', 'more', 'tasklist', 'taskkill', 'whoami', 'date', 'ver',
      'systeminfo', 'echo', 'set', 'cls', 'help'
    ];
    
    // Check if it's a built-in Windows command
    if (windowsCommands.includes(lowerCmd)) {
      return true;
    }
    
    // Check if it's a mapped Unix command
    if (COMMAND_MAP[lowerCmd]) {
      return true;
    }
    
    // Modern Windows also has these
    const modernWindowsCommands = ['curl', 'tar', 'ssh', 'git'];
    if (modernWindowsCommands.includes(lowerCmd)) {
      return true;
    }
  } else {
    // Unix-like systems - most commands should be available
    const commonUnixCommands = [
      'ls', 'cat', 'rm', 'mv', 'cp', 'mkdir', 'rmdir', 'touch', 'pwd', 'which',
      'grep', 'head', 'tail', 'wc', 'ps', 'kill', 'killall', 'curl', 'wget',
      'tar', 'unzip', 'uname', 'whoami', 'id', 'date', 'uptime'
    ];
    
    if (commonUnixCommands.includes(lowerCmd)) {
      return true;
    }
  }
  
  // For other commands, assume they might be available
  return true;
}

/**
 * Get platform-specific command suggestions
 */
export function getCommandSuggestions(command: string): string[] {
  const lowerCmd = command.toLowerCase();
  const suggestions: string[] = [];
  
  if (process.platform === 'win32') {
    // Suggest Windows alternatives
    const windowsSuggestions: Record<string, string[]> = {
      'ls': ['dir', 'Get-ChildItem'],
      'cat': ['type', 'Get-Content'],
      'rm': ['del', 'Remove-Item'],
      'mv': ['move', 'Move-Item'],
      'cp': ['copy', 'Copy-Item'],
      'grep': ['findstr', 'Select-String'],
      'ps': ['tasklist', 'Get-Process'],
      'kill': ['taskkill', 'Stop-Process'],
      'which': ['where', 'Get-Command'],
      'curl': ['curl', 'Invoke-WebRequest'],
      'wget': ['curl', 'Invoke-WebRequest']
    };
    
    if (windowsSuggestions[lowerCmd]) {
      suggestions.push(...windowsSuggestions[lowerCmd]);
    }
  } else {
    // Suggest Unix alternatives
    const unixSuggestions: Record<string, string[]> = {
      'dir': ['ls'],
      'type': ['cat'],
      'del': ['rm'],
      'move': ['mv'],
      'copy': ['cp'],
      'findstr': ['grep'],
      'tasklist': ['ps'],
      'taskkill': ['kill'],
      'where': ['which']
    };
    
    if (unixSuggestions[lowerCmd]) {
      suggestions.push(...unixSuggestions[lowerCmd]);
    }
  }
  
  return suggestions;
}

/**
 * Get command help for platform
 */
export function getCommandHelp(command: string): string {
  const lowerCmd = command.toLowerCase();
  
  const helpTexts: Record<string, string> = {
    'ls': 'List directory contents. Use -l for detailed view, -a for hidden files.',
    'dir': 'List directory contents. Use /Q for ownership, /A for hidden files.',
    'cat': 'Display file contents. Usage: cat <filename>',
    'type': 'Display file contents. Usage: type <filename>',
    'rm': 'Remove files/directories. Use -r for recursive, -f for force.',
    'del': 'Delete files. Use /S for recursive, /Q for quiet.',
    'grep': 'Search text patterns. Usage: grep <pattern> <files>',
    'findstr': 'Search text patterns. Usage: findstr <pattern> <files>',
    'ps': 'List running processes. Use -e for all processes.',
    'tasklist': 'List running processes. Use /FO LIST for detailed view.',
    'kill': 'Terminate processes. Usage: kill <PID>',
    'taskkill': 'Terminate processes. Usage: taskkill /PID <PID>'
  };
  
  return helpTexts[lowerCmd] || `No help available for command: ${command}`;
}

/**
 * Normalize path separators for platform
 */
export function normalizePath(path: string): string {
  if (process.platform === 'win32') {
    return path.replace(/\//g, '\\');
  } else {
    return path.replace(/\\/g, '/');
  }
}

/**
 * Get shell command for platform
 */
export function getShellCommand(): string[] {
  if (process.platform === 'win32') {
    return ['cmd', '/c'];
  } else {
    return ['sh', '-c'];
  }
}

/**
 * Escape command argument for platform
 */
export function escapeArgument(arg: string): string {
  if (process.platform === 'win32') {
    // Windows escaping
    if (arg.includes(' ') || arg.includes('"')) {
      return `"${arg.replace(/"/g, '""')}"`;
    }
    return arg;
  } else {
    // Unix escaping
    if (arg.includes(' ') || arg.includes("'") || arg.includes('"')) {
      return `'${arg.replace(/'/g, "'\"'\"'")}'`;
    }
    return arg;
  }
}
