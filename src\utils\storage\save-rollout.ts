/**
 * Session Rollout System
 * 
 * Handles automatic session persistence with best-effort async saving
 * Provides session recovery and management capabilities
 */

import { writeFileSync, existsSync, mkdirSync, readdirSync, readFileSync, unlinkSync } from 'fs';
import { join, dirname } from 'path';
import { homedir } from 'os';
import type { ResponseItem, SessionData, SessionMetadata } from '../../types/index.js';

// Session storage directory
const SESSIONS_DIR = join(homedir(), '.kritrima-ai', 'sessions');

// Maximum number of sessions to keep
const MAX_SESSIONS = 100;

/**
 * Get sessions directory path
 */
export function getSessionsDir(): string {
  return SESSIONS_DIR;
}

/**
 * Save session rollout (best-effort async)
 */
export function saveRollout(
  sessionId: string,
  items: ResponseItem[],
  config?: any
): void {
  // Best-effort async saving without blocking UI
  saveRolloutAsync(sessionId, items, config).catch(() => {
    // Silently fail - this is best-effort
  });
}

/**
 * Save session rollout asynchronously
 */
export async function saveRolloutAsync(
  sessionId: string,
  items: ResponseItem[],
  config?: any
): Promise<void> {
  try {
    // Ensure sessions directory exists
    if (!existsSync(SESSIONS_DIR)) {
      mkdirSync(SESSIONS_DIR, { recursive: true });
    }

    // Create session metadata
    const metadata: SessionMetadata = {
      id: sessionId,
      timestamp: Date.now(),
      model: config?.model || 'unknown',
      provider: config?.provider || 'unknown',
      itemCount: items.length,
      lastActivity: Date.now()
    };

    // Create session data
    const sessionData: SessionData = {
      metadata,
      items,
      config: config || {}
    };

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const filename = `rollout-${timestamp}-${sessionId}.json`;
    const filepath = join(SESSIONS_DIR, filename);

    // Save session data
    writeFileSync(filepath, JSON.stringify(sessionData, null, 2));

    // Cleanup old sessions
    await cleanupOldSessions();

  } catch (error) {
    // Log error but don't throw - this is best-effort
    console.warn('Warning: Could not save session rollout:', error);
  }
}

/**
 * Load session by ID
 */
export function loadSession(sessionId: string): SessionData | null {
  try {
    if (!existsSync(SESSIONS_DIR)) {
      return null;
    }

    // Find session file
    const files = readdirSync(SESSIONS_DIR);
    const sessionFile = files.find(file => file.includes(sessionId));

    if (!sessionFile) {
      return null;
    }

    const filepath = join(SESSIONS_DIR, sessionFile);
    const content = readFileSync(filepath, 'utf-8');
    const sessionData: SessionData = JSON.parse(content);

    return sessionData;

  } catch (error) {
    console.warn(`Warning: Could not load session ${sessionId}:`, error);
    return null;
  }
}

/**
 * Get saved sessions (alias for listSessions)
 */
export async function getSavedSessions(): Promise<SessionMetadata[]> {
  return listSessions();
}

/**
 * List all available sessions
 */
export function listSessions(): SessionMetadata[] {
  try {
    if (!existsSync(SESSIONS_DIR)) {
      return [];
    }

    const files = readdirSync(SESSIONS_DIR);
    const sessions: SessionMetadata[] = [];

    for (const file of files) {
      if (!file.startsWith('rollout-') || !file.endsWith('.json')) {
        continue;
      }

      try {
        const filepath = join(SESSIONS_DIR, file);
        const content = readFileSync(filepath, 'utf-8');
        const sessionData: SessionData = JSON.parse(content);
        sessions.push(sessionData.metadata);
      } catch (error) {
        // Skip corrupted session files
        continue;
      }
    }

    // Sort by timestamp (newest first)
    sessions.sort((a, b) => b.timestamp - a.timestamp);

    return sessions;

  } catch (error) {
    console.warn('Warning: Could not list sessions:', error);
    return [];
  }
}

/**
 * Delete session by ID
 */
export function deleteSession(sessionId: string): boolean {
  try {
    if (!existsSync(SESSIONS_DIR)) {
      return false;
    }

    const files = readdirSync(SESSIONS_DIR);
    const sessionFile = files.find(file => file.includes(sessionId));

    if (!sessionFile) {
      return false;
    }

    const filepath = join(SESSIONS_DIR, sessionFile);
    unlinkSync(filepath);

    return true;

  } catch (error) {
    console.warn(`Warning: Could not delete session ${sessionId}:`, error);
    return false;
  }
}

/**
 * Get session statistics
 */
export function getSessionStats(): {
  totalSessions: number;
  totalSize: number;
  oldestSession?: Date;
  newestSession?: Date;
} {
  try {
    const sessions = listSessions();
    
    if (sessions.length === 0) {
      return { totalSessions: 0, totalSize: 0 };
    }

    let totalSize = 0;
    
    // Calculate total size
    if (existsSync(SESSIONS_DIR)) {
      const files = readdirSync(SESSIONS_DIR);
      for (const file of files) {
        if (file.startsWith('rollout-') && file.endsWith('.json')) {
          try {
            const filepath = join(SESSIONS_DIR, file);
            const content = readFileSync(filepath, 'utf-8');
            totalSize += content.length;
          } catch (error) {
            // Skip files we can't read
          }
        }
      }
    }

    const timestamps = sessions.map(s => s.timestamp);
    const oldestTimestamp = Math.min(...timestamps);
    const newestTimestamp = Math.max(...timestamps);

    return {
      totalSessions: sessions.length,
      totalSize,
      oldestSession: new Date(oldestTimestamp),
      newestSession: new Date(newestTimestamp)
    };

  } catch (error) {
    console.warn('Warning: Could not get session stats:', error);
    return { totalSessions: 0, totalSize: 0 };
  }
}

/**
 * Cleanup old sessions to maintain storage limits
 */
async function cleanupOldSessions(): Promise<void> {
  try {
    const sessions = listSessions();
    
    if (sessions.length <= MAX_SESSIONS) {
      return;
    }

    // Sort by timestamp (oldest first for deletion)
    const sortedSessions = [...sessions].sort((a, b) => a.timestamp - b.timestamp);
    
    // Delete oldest sessions
    const sessionsToDelete = sortedSessions.slice(0, sessions.length - MAX_SESSIONS);
    
    for (const session of sessionsToDelete) {
      deleteSession(session.id);
    }

  } catch (error) {
    console.warn('Warning: Could not cleanup old sessions:', error);
  }
}

/**
 * Export session to file
 */
export function exportSession(sessionId: string, outputPath: string): boolean {
  try {
    const sessionData = loadSession(sessionId);
    if (!sessionData) {
      return false;
    }

    // Ensure output directory exists
    const outputDir = dirname(outputPath);
    if (!existsSync(outputDir)) {
      mkdirSync(outputDir, { recursive: true });
    }

    // Write session data
    writeFileSync(outputPath, JSON.stringify(sessionData, null, 2));
    return true;

  } catch (error) {
    console.warn(`Warning: Could not export session ${sessionId}:`, error);
    return false;
  }
}

/**
 * Import session from file
 */
export async function importSession(inputPath: string): Promise<string | null> {
  try {
    if (!existsSync(inputPath)) {
      return null;
    }

    const content = readFileSync(inputPath, 'utf-8');
    const sessionData: SessionData = JSON.parse(content);

    // Generate new session ID
    const newSessionId = generateSessionId();
    sessionData.metadata.id = newSessionId;
    sessionData.metadata.timestamp = Date.now();

    // Save imported session
    await saveRolloutAsync(newSessionId, sessionData.items, sessionData.config);

    return newSessionId;

  } catch (error) {
    console.warn(`Warning: Could not import session from ${inputPath}:`, error);
    return null;
  }
}

/**
 * Generate unique session ID
 */
function generateSessionId(): string {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
