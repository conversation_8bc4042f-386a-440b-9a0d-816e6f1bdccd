/**
 * Autonomous Agent Loop & Tool Calling System
 * 
 * The heart of the AI system implementing sophisticated autonomous workflow
 * <PERSON>les tool calling, conversation management, and state persistence
 */

import type { OpenAI } from 'openai';
import { createOpenAIClient } from '../openai-client.js';
import { loadConfig } from '../config.js';
import { logAgentExecution, logError, logInfo } from '../logger/log.js';
import type {
  AppConfig,
  ApprovalPolicy,
  ResponseItem,
  ResponseInputItem,
  ResponseOutputItem,
  ResponseFunctionToolCall,
  ResponseToolResult,
  ExecInput,
  FunctionTool
} from '../../types/index.js';
import { handleExecCommand } from './handle-exec-command.js';
import { convertMessagesToOpenAI, estimateTokenCount, debugMessageConversion } from '../responses.js';
import { toolRegistry, type ToolContext } from '../tools/tool-registry.js';
import { saveRollout, loadSession } from '../storage/save-rollout.js';
import { generateSystemPrompt } from '../prompts/system-prompt-manager.js';
import { createSystemMessage } from '../input-utils.js';

export interface AgentLoopConfig {
  model: string;
  provider: string;
  approvalPolicy: ApprovalPolicy;
  maxIterations?: number;
  timeout?: number;
  additionalWritableRoots?: string[];
  singlePass?: boolean;
  planningMode?: boolean;
  executionMode?: boolean;
  validationMode?: boolean;
  useFullContext?: boolean;
  enableStreaming?: boolean;
  enableToolRegistry?: boolean;
  enableSessionPersistence?: boolean;
  enableContextOptimization?: boolean;
  enablePerformanceMonitoring?: boolean;
  sessionId?: string;
  maxContextTokens?: number;
  contextCompressionThreshold?: number;
  // System prompt configuration
  enableSystemPrompt?: boolean;
  systemPromptMode?: 'general' | 'coding' | 'analysis' | 'planning' | 'execution' | 'validation' | 'debugging' | 'documentation' | 'testing';
  systemPromptContext?: 'cli' | 'interactive' | 'single-pass' | 'agent-loop' | 'batch' | 'streaming';
  customInstructions?: string;
  safetyLevel?: 'strict' | 'moderate' | 'permissive';
  verbosityLevel?: 'minimal' | 'normal' | 'detailed' | 'verbose';
}

export interface AgentLoopCallbacks {
  onDelta?: (delta: string) => void;
  onComplete?: (content: string) => void;
  onError?: (error: string) => void;
  onToolCall?: (toolCall: ResponseFunctionToolCall) => void;
  onToolResult?: (result: ResponseToolResult) => void;
  getCommandConfirmation?: (command: string[], workdir: string) => Promise<boolean>;
  onIterationStart?: (iteration: number) => void;
  onIterationComplete?: (iteration: number, result: any) => void;
  onContextOptimization?: (beforeTokens: number, afterTokens: number) => void;
  onSessionSave?: (sessionId: string) => void;
  onSessionRestore?: (sessionId: string) => void;
  onPerformanceMetrics?: (metrics: PerformanceMetrics) => void;
  onStateChange?: (state: AgentState) => void;
}

export interface PerformanceMetrics {
  totalExecutionTime: number;
  averageIterationTime: number;
  tokenUsage: number;
  toolCallCount: number;
  errorCount: number;
  successRate: number;
}

export type AgentState = 'idle' | 'thinking' | 'tool_calling' | 'waiting_approval' | 'error' | 'complete';

/**
 * Core Agent Loop Class
 */
export class AgentLoop {
  private model: string;
  private provider: string;
  private oai: OpenAI;
  private approvalPolicy: ApprovalPolicy;
  private transcript: ResponseInputItem[] = [];
  private cumulativeThinkingMs = 0;
  private additionalWritableRoots: string[];
  private config: AppConfig;

  // Enhanced capabilities
  private sessionId: string;
  private currentState: AgentState = 'idle';
  private performanceMetrics: PerformanceMetrics;
  private enableStreaming: boolean;
  private enableToolRegistry: boolean;
  private enableSessionPersistence: boolean;
  private enableContextOptimization: boolean;
  private enablePerformanceMonitoring: boolean;
  private maxContextTokens: number;
  private contextCompressionThreshold: number;
  private iterationTimes: number[] = [];
  private toolCallCount = 0;
  private errorCount = 0;
  private successfulIterations = 0;

  // System prompt capabilities
  private enableSystemPrompt: boolean;
  private systemPromptMode: string;
  private systemPromptContext: string;
  private customInstructions?: string;
  private safetyLevel: string;
  private verbosityLevel: string;

  constructor(config: AgentLoopConfig) {
    this.model = config.model;
    this.provider = config.provider;
    this.approvalPolicy = config.approvalPolicy;
    this.additionalWritableRoots = config.additionalWritableRoots || [];
    this.config = loadConfig();

    // Initialize enhanced capabilities
    this.sessionId = config.sessionId || this.generateSessionId();
    this.enableStreaming = config.enableStreaming ?? true;
    this.enableToolRegistry = config.enableToolRegistry ?? true;
    this.enableSessionPersistence = config.enableSessionPersistence ?? true;
    this.enableContextOptimization = config.enableContextOptimization ?? true;
    this.enablePerformanceMonitoring = config.enablePerformanceMonitoring ?? true;
    this.maxContextTokens = config.maxContextTokens || 32000;
    this.contextCompressionThreshold = config.contextCompressionThreshold || 0.8;

    // Initialize system prompt capabilities
    this.enableSystemPrompt = config.enableSystemPrompt ?? true;
    this.systemPromptMode = config.systemPromptMode || 'general';
    this.systemPromptContext = config.systemPromptContext || 'agent-loop';
    this.customInstructions = config.customInstructions;
    this.safetyLevel = config.safetyLevel || 'moderate';
    this.verbosityLevel = config.verbosityLevel || 'normal';

    // Initialize performance metrics
    this.performanceMetrics = {
      totalExecutionTime: 0,
      averageIterationTime: 0,
      tokenUsage: 0,
      toolCallCount: 0,
      errorCount: 0,
      successRate: 0
    };

    // Create OpenAI client
    this.oai = createOpenAIClient({
      provider: this.provider,
      timeout: config.timeout
    });

    logInfo(`AgentLoop initialized with session ID: ${this.sessionId}`);
  }

  /**
   * Generate a unique session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Add system prompt to transcript
   */
  private async addSystemPrompt(): Promise<void> {
    try {
      logInfo('Generating system prompt', {
        mode: this.systemPromptMode,
        context: this.systemPromptContext,
        provider: this.provider
      });

      const systemPromptResult = await generateSystemPrompt(
        this.systemPromptMode as any,
        this.systemPromptContext as any,
        this.config,
        {
          customInstructions: this.customInstructions,
          safetyLevel: this.safetyLevel as any,
          verbosityLevel: this.verbosityLevel as any,
          enableThinking: true,
          enablePlanning: this.systemPromptMode === 'planning',
          enableValidation: this.systemPromptMode === 'validation'
        }
      );

      const systemMessage = createSystemMessage(systemPromptResult.prompt);
      this.transcript.push(systemMessage);

      logInfo('System prompt added to transcript', {
        templateId: systemPromptResult.metadata.templateId,
        promptLength: systemPromptResult.prompt.length,
        tokenCount: systemPromptResult.metadata.tokenCount
      });

    } catch (error) {
      logError('Failed to generate system prompt', error instanceof Error ? error : new Error(String(error)));

      // Add fallback system prompt
      const fallbackPrompt = this.createFallbackSystemPrompt();
      const systemMessage = createSystemMessage(fallbackPrompt);
      this.transcript.push(systemMessage);
    }
  }

  /**
   * Create fallback system prompt
   */
  private createFallbackSystemPrompt(): string {
    return `You are Kritrima AI, an advanced and autonomous AI assistant with comprehensive tool calling and execution capabilities.

# Core Instructions
- Your main goal is to help users with their tasks and queries.
- You are designed to be helpful, accurate, and efficient.
- You have access to a wide range of tools and capabilities and can chain multiple tools together and combine them together to accomplish complex tasks automatically according to user instructions and approval policies.
- You should always follow safety guidelines and best practices.
- You should provide clear explanations and step-by-step guidance.
- You should be concise and avoid unnecessary details.
- You should be adaptive and adjust your approach based on user expertise and context.
- You should always think before you act.
- You should always validate your actions and provide feedback to the user.
- You should always ask for confirmation before making any changes to the system or user files according to the approval policy.
- You should always provide a summary of your actions and the results to the user.
- You should always thnink and create a detailed todo list for complex tasks and provide a plan before executing any actions.
- You should always check if the task is completed and if not, you should provide a reason why and ask for further instructions.
- You should always provide a way for the user to check the status of the task and the progress.

# Your Capabilities
- Be helpful, accurate, and efficient 
- Use available tools when appropriate
- Follow safety guidelines and best practices
- Provide clear explanations and step-by-step guidance

# Current Configuration
- Model: ${this.model}
- Provider: ${this.provider}
- Mode: ${this.systemPromptMode}
- Context: ${this.systemPromptContext}
- Safety Level: ${this.safetyLevel}
- Verbosity: ${this.verbosityLevel}

${this.customInstructions ? `# Custom Instructions\n${this.customInstructions}\n` : ''}

Ready to assist you with your tasks!`;
  }

  /**
   * Update agent state and notify callbacks
   */
  private updateState(newState: AgentState, callbacks?: AgentLoopCallbacks): void {
    if (this.currentState !== newState) {
      this.currentState = newState;
      callbacks?.onStateChange?.(newState);
      logAgentExecution('state_change', { state: newState });
    }
  }

  /**
   * Update performance metrics
   */
  private updatePerformanceMetrics(callbacks?: AgentLoopCallbacks): void {
    if (!this.enablePerformanceMonitoring) {return;}

    const totalIterations = this.successfulIterations + this.errorCount;
    this.performanceMetrics = {
      totalExecutionTime: this.cumulativeThinkingMs,
      averageIterationTime: this.iterationTimes.length > 0
        ? this.iterationTimes.reduce((a, b) => a + b, 0) / this.iterationTimes.length
        : 0,
      tokenUsage: this.performanceMetrics.tokenUsage,
      toolCallCount: this.toolCallCount,
      errorCount: this.errorCount,
      successRate: totalIterations > 0 ? this.successfulIterations / totalIterations : 0
    };

    callbacks?.onPerformanceMetrics?.(this.performanceMetrics);
  }

  /**
   * Optimize context by compressing old messages when token limit is approached
   */
  private async optimizeContext(callbacks?: AgentLoopCallbacks): Promise<void> {
    if (!this.enableContextOptimization) {return;}

    const messages = convertMessagesToOpenAI([...this.transcript]);
    const currentTokens = estimateTokenCount(messages);

    if (currentTokens > this.maxContextTokens * this.contextCompressionThreshold) {
      logInfo(`Context optimization triggered: ${currentTokens} tokens > ${this.maxContextTokens * this.contextCompressionThreshold}`);

      // Keep the first message (system prompt) and last few messages
      const keepCount = Math.min(5, this.transcript.length);
      const compressedTranscript = [
        ...this.transcript.slice(0, 1), // Keep first message
        {
          type: 'message' as const,
          role: 'system' as const,
          content: [{
            type: 'input_text' as const,
            text: `[Previous conversation compressed - ${this.transcript.length - keepCount - 1} messages summarized]`
          }],
          timestamp: Date.now()
        },
        ...this.transcript.slice(-keepCount) // Keep last few messages
      ];

      const newMessages = convertMessagesToOpenAI(compressedTranscript);
      const newTokens = estimateTokenCount(newMessages);

      this.transcript = compressedTranscript;

      logInfo(`Context optimized: ${currentTokens} -> ${newTokens} tokens`);
      callbacks?.onContextOptimization?.(currentTokens, newTokens);
    }
  }

  /**
   * Save session if persistence is enabled
   */
  private async saveSession(callbacks?: AgentLoopCallbacks): Promise<void> {
    if (!this.enableSessionPersistence) {return;}

    try {
      saveRollout(this.sessionId, [...this.transcript], {
        model: this.model,
        provider: this.provider,
        approvalPolicy: this.approvalPolicy
      });

      callbacks?.onSessionSave?.(this.sessionId);
      logInfo(`Session saved: ${this.sessionId}`);
    } catch (error) {
      logError('Failed to save session', error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * Execute agent loop with user input
   */
  async executeLoop(
    userInput: ResponseInputItem,
    callbacks?: AgentLoopCallbacks,
    maxIterations?: number
  ): Promise<ResponseItem[]>;

  async executeLoop(
    userInput: ResponseInputItem,
    options: {
      callbacks?: AgentLoopCallbacks;
      maxIterations?: number;
      singlePass?: boolean;
      planningMode?: boolean;
      executionMode?: boolean;
      validationMode?: boolean;
    }
  ): Promise<ResponseItem[]>;

  async executeLoop(
    userInput: ResponseInputItem,
    callbacksOrOptions: AgentLoopCallbacks | {
      callbacks?: AgentLoopCallbacks;
      maxIterations?: number;
      singlePass?: boolean;
      planningMode?: boolean;
      executionMode?: boolean;
      validationMode?: boolean;
    } = {},
    maxIterations: number = 10
  ): Promise<ResponseItem[]> {
    // Handle overloaded parameters
    let callbacks: AgentLoopCallbacks = {};
    let actualMaxIterations = maxIterations;
    let singlePass = false;

    if ('callbacks' in callbacksOrOptions || 'maxIterations' in callbacksOrOptions) {
      // New options format
      const options = callbacksOrOptions as any;
      callbacks = options.callbacks || {};
      actualMaxIterations = options.maxIterations || 10;
      singlePass = options.singlePass || false;
      // Note: planningMode, executionMode, validationMode are reserved for future use
    } else {
      // Legacy callbacks format
      callbacks = callbacksOrOptions as AgentLoopCallbacks;
    }

    // Single pass mode limits iterations to 1
    if (singlePass) {
      actualMaxIterations = 1;
    }
    const startTime = Date.now();
    const results: ResponseItem[] = [];

    try {
      // Update state to thinking
      this.updateState('thinking', callbacks);

      // Generate and add system prompt if enabled and not already present
      if (this.enableSystemPrompt && this.transcript.length === 0) {
        await this.addSystemPrompt();
      }

      // Add user input to transcript
      this.transcript.push(userInput);
      results.push(userInput);

      // Optimize context if needed
      await this.optimizeContext(callbacks);

      logAgentExecution('loop_start', {
        model: this.model,
        provider: this.provider,
        approvalPolicy: this.approvalPolicy,
        sessionId: this.sessionId
      });

      let iteration = 0;
      while (iteration < actualMaxIterations) {
        iteration++;

        // Notify iteration start
        callbacks.onIterationStart?.(iteration);
        logAgentExecution('iteration_start', { iteration });

        // Convert transcript to OpenAI format
        const messages = convertMessagesToOpenAI([...this.transcript]);

        // Debug message conversion if in debug mode
        if (process.env.DEBUG) {
          const debugInfo = debugMessageConversion([...this.transcript]);
          logAgentExecution('message_conversion_debug', debugInfo);
        }

        // Validate messages array
        if (messages.length === 0) {
          const debugInfo = debugMessageConversion([...this.transcript]);
          const errorDetails = `Transcript items: ${debugInfo.inputItems} inputs, ${debugInfo.outputItems} outputs. Details: ${JSON.stringify(debugInfo.details)}`;
          throw new Error(`No valid messages to send to AI provider. Check that your input contains text content. ${errorDetails}`);
        }

        // Check token limits and update metrics
        const tokenCount = estimateTokenCount(messages);
        this.performanceMetrics.tokenUsage += tokenCount;
        logAgentExecution('token_count', { tokens: tokenCount, messageCount: messages.length });

        // Prepare tools (enhanced with tool registry if enabled)
        const tools = this.getAvailableTools();

        // Make AI request
        const iterationStartTime = Date.now();
        
        try {
          const completion = await this.oai.chat.completions.create({
            model: this.model,
            messages,
            tools: tools.length > 0 ? tools : undefined,
            tool_choice: tools.length > 0 ? 'auto' : undefined,
            temperature: this.config.temperature || 0.7,
            max_tokens: this.config.maxTokens || 4096,
            stream: false
          } as any);

          const iterationTime = Date.now() - iterationStartTime;
          this.cumulativeThinkingMs += iterationTime;
          this.iterationTimes.push(iterationTime);
          this.successfulIterations++;

          const choice = completion.choices?.[0];
          if (!choice) {
            throw new Error('No response choice received');
          }

          // Handle content
          const content = choice.message?.content || '';
          if (content.trim()) {
            callbacks.onDelta?.(content);
            callbacks.onComplete?.(content);

            const assistantResponse: ResponseOutputItem = {
              role: 'assistant',
              content,
              type: 'output',
              timestamp: Date.now(),
              metadata: {
                model: this.model,
                provider: this.provider,
                thinkingTime: iterationTime
              }
            };

            results.push(assistantResponse);
          }

          // Handle function calls
          if (choice.message?.tool_calls) {
            this.updateState('tool_calling', callbacks);

            for (const toolCall of choice.message.tool_calls) {
              if (toolCall.type === 'function') {
                this.toolCallCount++;

                const functionCall: ResponseFunctionToolCall = {
                  id: toolCall.id,
                  name: toolCall.function.name,
                  arguments: toolCall.function.arguments,
                  type: 'function_call',
                  timestamp: Date.now()
                };

                results.push(functionCall);
                callbacks.onToolCall?.(functionCall);

                // Execute function call
                const toolResults = await this.handleFunctionCall(functionCall, callbacks);
                results.push(...toolResults);
              }
            }

            // Update performance metrics after tool calls
            this.updatePerformanceMetrics(callbacks);
          } else {
            // No function calls, conversation is complete
            this.updateState('complete', callbacks);

            // Save session if enabled
            await this.saveSession(callbacks);

            // Final performance metrics update
            this.updatePerformanceMetrics(callbacks);

            logAgentExecution('loop_complete', {
              iterations: iteration,
              totalTime: Date.now() - startTime,
              sessionId: this.sessionId
            });

            callbacks.onIterationComplete?.(iteration, results);
            return results;
          }

        } catch (error) {
          this.errorCount++;
          this.updateState('error', callbacks);

          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          logError('Agent loop iteration failed', error instanceof Error ? error : new Error(errorMessage));
          callbacks.onError?.(errorMessage);

          // Update performance metrics even on error
          this.updatePerformanceMetrics(callbacks);

          throw error;
        }
      }

      // Max iterations reached
      this.updateState('complete', callbacks);
      await this.saveSession(callbacks);
      this.updatePerformanceMetrics(callbacks);

      logAgentExecution('loop_max_iterations', { maxIterations: actualMaxIterations });
      return results;

    } catch (error) {
      this.updateState('error', callbacks);

      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logError('Agent loop failed', error instanceof Error ? error : new Error(errorMessage));
      callbacks.onError?.(errorMessage);

      // Save session even on error for recovery
      await this.saveSession(callbacks);
      this.updatePerformanceMetrics(callbacks);

      throw error;
    }
  }

  /**
   * Handle function call execution (enhanced with tool registry)
   */
  private async handleFunctionCall(
    functionCall: ResponseFunctionToolCall,
    callbacks: AgentLoopCallbacks
  ): Promise<ResponseToolResult[]> {
    const { name, arguments: argsString } = functionCall;

    try {
      // Parse arguments
      let args: any;
      try {
        args = JSON.parse(argsString);
      } catch {
        throw new Error(`Invalid function arguments: ${argsString}`);
      }

      // Try tool registry first if enabled
      if (this.enableToolRegistry) {
        const tool = toolRegistry.getTool(name);
        if (tool) {
          const context: ToolContext = {
            workingDirectory: process.cwd(),
            environment: process.env as Record<string, string>,
            capabilities: ['file_read', 'file_write', 'command_execute']
          };

          const toolResult = await toolRegistry.executeTool(name, args, context);

          const result: ResponseToolResult = {
            id: functionCall.id,
            result: toolResult.output || toolResult.data || 'Tool executed successfully',
            success: toolResult.success,
            type: 'tool_result',
            timestamp: Date.now(),
            metadata: {
              ...toolResult.metadata,
              toolId: name,
              executionTime: toolResult.executionTime
            } as any
          };

          return [result];
        }
      }

      // Fallback to legacy shell command handling
      if (name === 'shell' || name === 'local_shell') {
        return await this.handleShellCommand(args, functionCall.id, callbacks);
      }

      throw new Error(`Unknown function: ${name}`);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorResult: ResponseToolResult = {
        id: functionCall.id,
        result: `Error: ${errorMessage}`,
        success: false,
        type: 'tool_result',
        timestamp: Date.now()
      };

      return [errorResult];
    }
  }

  /**
   * Handle shell command execution
   */
  private async handleShellCommand(
    args: any,
    toolCallId: string,
    callbacks: AgentLoopCallbacks
  ): Promise<ResponseToolResult[]> {
    try {
      const execInput: ExecInput = {
        command: args.command || [],
        workdir: args.workdir || process.cwd(),
        timeout: args.timeout || 30000
      };

      // Get command confirmation if needed
      const needsApproval = this.approvalPolicy === 'suggest' || 
                           (this.approvalPolicy === 'auto-edit' && !this.isCommandSafe(execInput.command));

      if (needsApproval && callbacks.getCommandConfirmation) {
        const approved = await callbacks.getCommandConfirmation(execInput.command, execInput.workdir || process.cwd());
        if (!approved) {
          const deniedResult: ResponseToolResult = {
            id: toolCallId,
            result: 'Command execution denied by user',
            success: false,
            type: 'tool_result',
            timestamp: Date.now(),
            metadata: {
              command: execInput.command,
              workdir: execInput.workdir
            }
          };
          return [deniedResult];
        }
      }

      // Execute command
      const execResult = await handleExecCommand(
        execInput,
        this.config,
        this.approvalPolicy,
        this.additionalWritableRoots
      );

      const toolResult: ResponseToolResult = {
        id: toolCallId,
        result: execResult.output,
        success: execResult.success,
        type: 'tool_result',
        timestamp: Date.now(),
        metadata: {
          command: execResult.command,
          workdir: execResult.workdir,
          exitCode: execResult.exitCode,
          duration: execResult.duration
        }
      };

      callbacks.onToolResult?.(toolResult);
      return [toolResult];

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorResult: ResponseToolResult = {
        id: toolCallId,
        result: `Error executing command: ${errorMessage}`,
        success: false,
        type: 'tool_result',
        timestamp: Date.now()
      };

      return [errorResult];
    }
  }

  /**
   * Check if command is safe for auto-approval
   */
  private isCommandSafe(command: string[]): boolean {
    const safeCommands = this.config.safeCommands || [];
    const commandName = command[0]?.toLowerCase();
    return safeCommands.includes(commandName);
  }

  /**
   * Get available tools based on approval policy and tool registry
   */
  private getAvailableTools(): FunctionTool[] {
    const tools: FunctionTool[] = [];

    // Add tools from tool registry if enabled
    if (this.enableToolRegistry) {
      const registryTools = toolRegistry.getAllTools();

      for (const tool of registryTools) {
        // Convert tool registry format to OpenAI function format
        const functionTool: FunctionTool = {
          type: 'function',
          function: {
            name: tool.id,
            description: tool.description,
            parameters: {
              type: 'object',
              properties: {},
              required: []
            }
          }
        };

        // Convert parameters
        for (const param of tool.parameters) {
          functionTool.function.parameters.properties[param.name] = {
            type: param.type,
            description: param.description
          };

          if (param.required) {
            functionTool.function.parameters.required.push(param.name);
          }
        }

        tools.push(functionTool);
      }
    }

    // Always include shell tool for backward compatibility
    tools.push({
      type: 'function',
      function: {
        name: 'shell',
        description: 'Execute shell commands and return their output. Use this to run system commands, file operations, and interact with the environment.',
        parameters: {
          type: 'object',
          properties: {
            command: {
              type: 'array',
              items: { type: 'string' },
              description: 'The command to execute as an array of strings (command and arguments)'
            },
            workdir: {
              type: 'string',
              description: 'Working directory for command execution (optional, defaults to current directory)'
            },
            timeout: {
              type: 'number',
              description: 'Timeout in milliseconds (optional, defaults to 30000)'
            }
          },
          required: ['command']
        }
      }
    });

    logInfo(`Available tools: ${tools.map(t => t.function.name).join(', ')}`);
    return tools;
  }

  /**
   * Get conversation transcript
   */
  getTranscript(): ResponseInputItem[] {
    return [...this.transcript];
  }

  /**
   * Clear conversation transcript
   */
  clearTranscript(): void {
    this.transcript = [];
  }

  /**
   * Get cumulative thinking time
   */
  getCumulativeThinkingTime(): number {
    return this.cumulativeThinkingMs;
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<AgentLoopConfig>): void {
    if (newConfig.model) {this.model = newConfig.model;}
    if (newConfig.provider) {this.provider = newConfig.provider;}
    if (newConfig.approvalPolicy) {this.approvalPolicy = newConfig.approvalPolicy;}
    if (newConfig.additionalWritableRoots) {this.additionalWritableRoots = newConfig.additionalWritableRoots;}

    // Update enhanced capabilities
    if (newConfig.enableStreaming !== undefined) {this.enableStreaming = newConfig.enableStreaming;}
    if (newConfig.enableToolRegistry !== undefined) {this.enableToolRegistry = newConfig.enableToolRegistry;}
    if (newConfig.enableSessionPersistence !== undefined) {this.enableSessionPersistence = newConfig.enableSessionPersistence;}
    if (newConfig.enableContextOptimization !== undefined) {this.enableContextOptimization = newConfig.enableContextOptimization;}
    if (newConfig.enablePerformanceMonitoring !== undefined) {this.enablePerformanceMonitoring = newConfig.enablePerformanceMonitoring;}
    if (newConfig.maxContextTokens) {this.maxContextTokens = newConfig.maxContextTokens;}
    if (newConfig.contextCompressionThreshold) {this.contextCompressionThreshold = newConfig.contextCompressionThreshold;}

    // Update system prompt capabilities
    if (newConfig.enableSystemPrompt !== undefined) {this.enableSystemPrompt = newConfig.enableSystemPrompt;}
    if (newConfig.systemPromptMode) {this.systemPromptMode = newConfig.systemPromptMode;}
    if (newConfig.systemPromptContext) {this.systemPromptContext = newConfig.systemPromptContext;}
    if (newConfig.customInstructions !== undefined) {this.customInstructions = newConfig.customInstructions;}
    if (newConfig.safetyLevel) {this.safetyLevel = newConfig.safetyLevel;}
    if (newConfig.verbosityLevel) {this.verbosityLevel = newConfig.verbosityLevel;}

    // Recreate OpenAI client if provider changed
    if (newConfig.provider || newConfig.model) {
      this.oai = createOpenAIClient({
        provider: this.provider,
        timeout: newConfig.timeout
      });
    }
  }

  /**
   * Get current agent state
   */
  getCurrentState(): AgentState {
    return this.currentState;
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics(): PerformanceMetrics {
    return { ...this.performanceMetrics };
  }

  /**
   * Get session ID
   */
  getSessionId(): string {
    return this.sessionId;
  }

  /**
   * Load session from storage
   */
  async loadSession(sessionId: string): Promise<boolean> {
    try {
      const sessionData = loadSession(sessionId);
      if (sessionData) {
        this.transcript = sessionData.items.filter(item =>
          item.type === 'input' || item.type === 'message'
        ) as ResponseInputItem[];
        this.sessionId = sessionId;

        logInfo(`Session loaded: ${sessionId} with ${this.transcript.length} items`);
        return true;
      }
      return false;
    } catch (error) {
      logError('Failed to load session', error instanceof Error ? error : new Error(String(error)));
      return false;
    }
  }

  /**
   * Reset agent state
   */
  reset(): void {
    this.transcript = [];
    this.cumulativeThinkingMs = 0;
    this.iterationTimes = [];
    this.toolCallCount = 0;
    this.errorCount = 0;
    this.successfulIterations = 0;
    this.currentState = 'idle';
    this.sessionId = this.generateSessionId();

    // Reset performance metrics
    this.performanceMetrics = {
      totalExecutionTime: 0,
      averageIterationTime: 0,
      tokenUsage: 0,
      toolCallCount: 0,
      errorCount: 0,
      successRate: 0
    };

    logInfo(`Agent reset with new session ID: ${this.sessionId}`);
  }
}
