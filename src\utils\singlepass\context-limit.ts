/**
 * Context Limit Management for Single-Pass Mode
 * 
 * Provides size calculation and optimization strategies for managing
 * context limits in full-context mode
 */

import { statSync, readdirSync } from 'fs';
import { join, relative, extname } from 'path';
import { logInfo, logError } from '../logger/log.js';

export interface FileContent {
  path: string;
  content: string;
  size: number;
  type: 'text' | 'binary';
  language?: string;
}

export interface DirectoryStats {
  totalFiles: number;
  totalSize: number;
  textFiles: number;
  binaryFiles: number;
  largestFile: { path: string; size: number };
  filesByExtension: Record<string, number>;
}

export interface ContextLimitOptions {
  maxTotalSize: number;
  maxFileSize: number;
  excludePatterns: string[];
  includeExtensions: string[];
  priorityDirectories: string[];
  compressionRatio: number;
}

/**
 * Compute size map for files and directories
 */
export function computeSizeMap(
  root: string,
  files: Array<FileContent>,
): [Record<string, number>, Record<string, number>] {
  const fileSizeMap: Record<string, number> = {};
  const dirSizeMap: Record<string, number> = {};

  // Calculate file sizes
  for (const file of files) {
    const relativePath = relative(root, file.path);
    fileSizeMap[relativePath] = file.size;
  }

  // Calculate directory sizes
  const directories = new Set<string>();
  for (const file of files) {
    const relativePath = relative(root, file.path);
    const pathParts = relativePath.split('/');
    
    // Add all parent directories
    for (let i = 1; i <= pathParts.length; i++) {
      const dirPath = pathParts.slice(0, i).join('/');
      if (dirPath) {
        directories.add(dirPath);
      }
    }
  }

  // Sum up sizes for each directory
  for (const dir of directories) {
    let totalSize = 0;
    for (const [filePath, size] of Object.entries(fileSizeMap)) {
      if (filePath.startsWith(dir + '/') || filePath === dir) {
        totalSize += size;
      }
    }
    dirSizeMap[dir] = totalSize;
  }

  return [fileSizeMap, dirSizeMap];
}

/**
 * Analyze directory structure and file distribution
 */
export function analyzeDirectory(rootPath: string): DirectoryStats {
  const stats: DirectoryStats = {
    totalFiles: 0,
    totalSize: 0,
    textFiles: 0,
    binaryFiles: 0,
    largestFile: { path: '', size: 0 },
    filesByExtension: {}
  };

  try {
    walkDirectory(rootPath, (filePath, stat) => {
      stats.totalFiles++;
      stats.totalSize += stat.size;

      if (stat.size > stats.largestFile.size) {
        stats.largestFile = { path: relative(rootPath, filePath), size: stat.size };
      }

      const ext = extname(filePath).toLowerCase();
      stats.filesByExtension[ext] = (stats.filesByExtension[ext] || 0) + 1;

      if (isTextFile(filePath)) {
        stats.textFiles++;
      } else {
        stats.binaryFiles++;
      }
    });

  } catch (error) {
    logError('Failed to analyze directory', error instanceof Error ? error : new Error(String(error)));
  }

  return stats;
}

/**
 * Optimize file selection for context limits
 */
export function optimizeForContextLimit(
  files: Array<FileContent>,
  options: ContextLimitOptions
): {
  selectedFiles: Array<FileContent>;
  excludedFiles: Array<{ path: string; reason: string; size: number }>;
  totalSize: number;
  compressionEstimate: number;
} {
  const selectedFiles: Array<FileContent> = [];
  const excludedFiles: Array<{ path: string; reason: string; size: number }> = [];
  let totalSize = 0;

  // Sort files by priority
  const prioritizedFiles = prioritizeFiles(files, options);

  for (const file of prioritizedFiles) {
    // Check file size limit
    if (file.size > options.maxFileSize) {
      excludedFiles.push({
        path: file.path,
        reason: `File too large (${file.size} > ${options.maxFileSize})`,
        size: file.size
      });
      continue;
    }

    // Check if adding this file would exceed total size limit
    const estimatedSize = totalSize + file.size;
    if (estimatedSize > options.maxTotalSize) {
      excludedFiles.push({
        path: file.path,
        reason: `Would exceed total size limit (${estimatedSize} > ${options.maxTotalSize})`,
        size: file.size
      });
      continue;
    }

    // Check exclude patterns
    if (shouldExcludeFile(file.path, options.excludePatterns)) {
      excludedFiles.push({
        path: file.path,
        reason: 'Matches exclude pattern',
        size: file.size
      });
      continue;
    }

    // Check include extensions (if specified)
    if (options.includeExtensions.length > 0) {
      const ext = extname(file.path).toLowerCase();
      if (!options.includeExtensions.includes(ext)) {
        excludedFiles.push({
          path: file.path,
          reason: 'Extension not in include list',
          size: file.size
        });
        continue;
      }
    }

    selectedFiles.push(file);
    totalSize += file.size;
  }

  const compressionEstimate = Math.round(totalSize * options.compressionRatio);

  logInfo('Context optimization completed', {
    totalFiles: files.length,
    selectedFiles: selectedFiles.length,
    excludedFiles: excludedFiles.length,
    totalSize,
    compressionEstimate
  });

  return {
    selectedFiles,
    excludedFiles,
    totalSize,
    compressionEstimate
  };
}

/**
 * Prioritize files based on importance and directory priority
 */
function prioritizeFiles(
  files: Array<FileContent>,
  options: ContextLimitOptions
): Array<FileContent> {
  return files.sort((a, b) => {
    // Priority directories come first
    const aPriority = getPriorityScore(a.path, options.priorityDirectories);
    const bPriority = getPriorityScore(b.path, options.priorityDirectories);
    
    if (aPriority !== bPriority) {
      return bPriority - aPriority;
    }

    // Smaller files come first (more likely to fit)
    return a.size - b.size;
  });
}

/**
 * Get priority score for a file path
 */
function getPriorityScore(filePath: string, priorityDirectories: string[]): number {
  for (let i = 0; i < priorityDirectories.length; i++) {
    if (filePath.startsWith(priorityDirectories[i])) {
      return priorityDirectories.length - i;
    }
  }
  return 0;
}

/**
 * Check if file should be excluded based on patterns
 */
function shouldExcludeFile(filePath: string, excludePatterns: string[]): boolean {
  for (const pattern of excludePatterns) {
    if (filePath.includes(pattern)) {
      return true;
    }
  }
  return false;
}

/**
 * Check if file is likely a text file
 */
function isTextFile(filePath: string): boolean {
  const textExtensions = [
    '.txt', '.md', '.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.c', '.cpp',
    '.h', '.hpp', '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala',
    '.html', '.css', '.scss', '.sass', '.less', '.xml', '.json', '.yaml', '.yml',
    '.toml', '.ini', '.cfg', '.conf', '.sh', '.bash', '.zsh', '.fish', '.ps1',
    '.sql', '.r', '.m', '.pl', '.lua', '.vim', '.dockerfile', '.makefile'
  ];

  const ext = extname(filePath).toLowerCase();
  return textExtensions.includes(ext);
}

/**
 * Walk directory recursively
 */
function walkDirectory(
  dirPath: string,
  callback: (filePath: string, stat: any) => void,
  maxDepth = 10,
  currentDepth = 0
): void {
  if (currentDepth >= maxDepth) {return;}

  try {
    const entries = readdirSync(dirPath);
    
    for (const entry of entries) {
      const fullPath = join(dirPath, entry);
      const stat = statSync(fullPath);
      
      if (stat.isFile()) {
        callback(fullPath, stat);
      } else if (stat.isDirectory() && !entry.startsWith('.')) {
        walkDirectory(fullPath, callback, maxDepth, currentDepth + 1);
      }
    }
  } catch (error) {
    // Skip directories we can't read
  }
}

/**
 * Get default context limit options
 */
export function getDefaultContextLimitOptions(): ContextLimitOptions {
  return {
    maxTotalSize: 1024 * 1024, // 1MB
    maxFileSize: 100 * 1024,   // 100KB
    excludePatterns: [
      'node_modules',
      '.git',
      'dist',
      'build',
      '.next',
      '.nuxt',
      'coverage',
      '.nyc_output',
      'logs',
      '*.log',
      '*.tmp',
      '*.cache'
    ],
    includeExtensions: [], // Empty means include all text files
    priorityDirectories: [
      'src',
      'lib',
      'app',
      'components',
      'utils',
      'services'
    ],
    compressionRatio: 0.7 // Assume 30% compression
  };
}
