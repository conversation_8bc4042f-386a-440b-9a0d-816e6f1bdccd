/**
 * Model Selection Overlay
 * 
 * Provides dynamic model and provider selection with real-time model fetching
 * Supports tab navigation between provider and model selection
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Box, Text, useInput } from 'ink';
import { fetchModels } from '../../utils/model-utils.js';
import { getAvailableProviders } from '../../utils/providers.js';
import { getApiKey } from '../../utils/config.js';
import type { ProviderName } from '../../types/index.js';

interface ModelOverlayProps {
  currentProvider: string;
  currentModel: string;
  onProviderChange: (provider: ProviderName) => void;
  onModelChange: (model: string) => void;
  onClose: () => void;
  visible: boolean;
}

type TabMode = 'provider' | 'model';

export function ModelOverlay({
  currentProvider,
  currentModel,
  onProviderChange,
  onModelChange,
  onClose,
  visible
}: ModelOverlayProps) {
  const [activeTab, setActiveTab] = useState<TabMode>('provider');
  const [selectedProviderIndex, setSelectedProviderIndex] = useState(0);
  const [selectedModelIndex, setSelectedModelIndex] = useState(0);
  const [availableModels, setAvailableModels] = useState<string[]>([]);
  const [loadingModels, setLoadingModels] = useState(false);
  const [modelError, setModelError] = useState<string | null>(null);

  // Get available providers
  const providers = getAvailableProviders();

  // Initialize selected provider index
  useEffect(() => {
    const providerIndex = providers.findIndex(p => p === currentProvider);
    if (providerIndex >= 0) {
      setSelectedProviderIndex(providerIndex);
    }
  }, [currentProvider, providers]);

  // Load models when provider changes
  useEffect(() => {
    if (activeTab === 'model') {
      loadModelsForProvider(providers[selectedProviderIndex]);
    }
  }, [activeTab, selectedProviderIndex, providers]);

  // Initialize selected model index
  useEffect(() => {
    const modelIndex = availableModels.findIndex(m => m === currentModel);
    if (modelIndex >= 0) {
      setSelectedModelIndex(modelIndex);
    } else {
      setSelectedModelIndex(0);
    }
  }, [currentModel, availableModels]);

  /**
   * Load models for selected provider
   */
  const loadModelsForProvider = useCallback(async (provider: string) => {
    setLoadingModels(true);
    setModelError(null);

    try {
      // Check if API key is available
      const apiKey = getApiKey(provider);
      if (!apiKey) {
        setModelError(`No API key configured for ${provider}`);
        setAvailableModels([]);
        return;
      }

      const models = await fetchModels(provider);
      setAvailableModels(models);
      
      if (models.length === 0) {
        setModelError(`No models available for ${provider}`);
      }
    } catch (error) {
      setModelError(error instanceof Error ? error.message : 'Failed to load models');
      setAvailableModels([]);
    } finally {
      setLoadingModels(false);
    }
  }, []);

  // Handle keyboard input
  useInput((input, key) => {
    if (!visible) {return;}

    // Close overlay
    if (key.escape) {
      onClose();
      return;
    }

    // Tab navigation
    if (key.tab) {
      setActiveTab(activeTab === 'provider' ? 'model' : 'provider');
      return;
    }

    // Navigation within tabs
    if (key.upArrow) {
      if (activeTab === 'provider') {
        setSelectedProviderIndex(Math.max(0, selectedProviderIndex - 1));
      } else {
        setSelectedModelIndex(Math.max(0, selectedModelIndex - 1));
      }
      return;
    }

    if (key.downArrow) {
      if (activeTab === 'provider') {
        setSelectedProviderIndex(Math.min(providers.length - 1, selectedProviderIndex + 1));
      } else {
        setSelectedModelIndex(Math.min(availableModels.length - 1, selectedModelIndex + 1));
      }
      return;
    }

    // Selection
    if (key.return) {
      if (activeTab === 'provider') {
        const selectedProvider = providers[selectedProviderIndex] as ProviderName;
        onProviderChange(selectedProvider);
        setActiveTab('model'); // Switch to model selection
      } else {
        const selectedModel = availableModels[selectedModelIndex];
        if (selectedModel) {
          onModelChange(selectedModel);
          onClose();
        }
      }
      return;
    }

    // Quick provider selection by letter
    if (activeTab === 'provider' && input && !key.ctrl && !key.meta) {
      const letter = input.toLowerCase();
      const providerIndex = providers.findIndex(p => p.toLowerCase().startsWith(letter));
      if (providerIndex >= 0) {
        setSelectedProviderIndex(providerIndex);
      }
    }
  });

  if (!visible) {
    return null;
  }

  return (
    <Box
      position="absolute"
      borderStyle="double"
      borderColor="cyan"
      flexDirection="column"
    >
      {/* Header */}
      <Box paddingX={2} paddingY={1} borderStyle="single" borderColor="gray">
        <Text color="cyan" bold>
          Model & Provider Selection
        </Text>
        <Box>
          <Text color="gray">
            Tab: Switch tabs • ↑↓: Navigate • Enter: Select • Esc: Close
          </Text>
        </Box>
      </Box>

      {/* Tab Headers */}
      <Box paddingX={2} paddingY={1}>
        <Box>
          <Text 
            color={activeTab === 'provider' ? 'white' : 'gray'}
            backgroundColor={activeTab === 'provider' ? 'blue' : undefined}
            bold={activeTab === 'provider'}
          >
            {' Provider '}
          </Text>
        </Box>
        <Box>
          <Text 
            color={activeTab === 'model' ? 'white' : 'gray'}
            backgroundColor={activeTab === 'model' ? 'blue' : undefined}
            bold={activeTab === 'model'}
          >
            {' Model '}
          </Text>
        </Box>
      </Box>

      {/* Content */}
      <Box flexGrow={1} paddingX={2} paddingBottom={1}>
        {activeTab === 'provider' ? (
          <Box flexDirection="column">
            <Text color="blue" bold>
              Available Providers:
            </Text>
            {providers.map((provider, index) => {
              const isSelected = index === selectedProviderIndex;
              const isCurrent = provider === currentProvider;
              const hasApiKey = !!getApiKey(provider);

              return (
                <Box key={provider}>
                  <Text
                    color={isSelected ? 'black' : hasApiKey ? 'green' : 'red'}
                    backgroundColor={isSelected ? 'cyan' : undefined}
                    bold={isSelected || isCurrent}
                  >
                    {isSelected ? '► ' : '  '}
                    {provider.padEnd(12)}
                    {hasApiKey ? ' ✓' : ' ✗'}
                    {isCurrent ? ' (current)' : ''}
                  </Text>
                </Box>
              );
            })}
            <Box>
              <Text color="gray" dimColor>
                ✓ = API key configured, ✗ = API key missing
              </Text>
            </Box>
          </Box>
        ) : (
          <Box flexDirection="column">
            <Text color="blue" bold>
              Models for {providers[selectedProviderIndex]}:
            </Text>
            
            {loadingModels ? (
              <Text color="yellow">Loading models...</Text>
            ) : modelError ? (
              <Text color="red">{modelError}</Text>
            ) : availableModels.length === 0 ? (
              <Text color="gray">No models available</Text>
            ) : (
              <>
                {availableModels.map((model, index) => {
                  const isSelected = index === selectedModelIndex;
                  const isCurrent = model === currentModel;

                  return (
                    <Box key={model}>
                      <Text
                        color={isSelected ? 'black' : 'white'}
                        backgroundColor={isSelected ? 'cyan' : undefined}
                        bold={isSelected || isCurrent}
                      >
                        {isSelected ? '► ' : '  '}
                        {model}
                        {isCurrent ? ' (current)' : ''}
                      </Text>
                    </Box>
                  );
                })}
                <Box>
                  <Text color="gray" dimColor>
                    {availableModels.length} models available
                  </Text>
                </Box>
              </>
            )}
          </Box>
        )}
      </Box>

      {/* Footer */}
      <Box paddingX={2} paddingY={1} borderStyle="single" borderColor="gray">
        <Text color="gray">
          Current: {currentProvider}/{currentModel}
        </Text>
      </Box>
    </Box>
  );
}
