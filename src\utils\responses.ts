/**
 * Response Processing System
 * 
 * Handles real-time streaming responses from AI providers
 * Processes different event types and manages conversation flow
 */

import type { OpenAI } from 'openai';
import type { ResponseItem, ResponseOutputItem, ResponseFunctionToolCall } from '../types/index.js';

// Response event types for streaming
export interface ResponseEvent {
  type: 'response.created' | 'response.output_text.delta' | 'response.function_call_arguments.done' | 'response.completed' | 'response.error';
  delta?: string;
  content?: string;
  functionCall?: ResponseFunctionToolCall;
  error?: string;
  metadata?: any;
}

// Input for creating responses
export interface ResponseCreateInput {
  messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string | Array<{ type: string; text?: string; image_url?: { url: string } }>;
  }>;
  model: string;
  temperature?: number;
  max_tokens?: number;
  tools?: Array<{
    type: 'function';
    function: {
      name: string;
      description: string;
      parameters: any;
    };
  }>;
  tool_choice?: 'auto' | 'none' | { type: 'function'; function: { name: string } };
  stream?: boolean;
}

/**
 * Stream responses from OpenAI completion
 */
export async function* streamResponses(
  input: ResponseCreateInput,
  completion: AsyncIterable<OpenAI.ChatCompletionChunk>
): AsyncGenerator<ResponseEvent> {
  let accumulatedContent = '';
  let functionCall: Partial<ResponseFunctionToolCall> | null = null;

  try {
    // Emit response created event
    yield {
      type: 'response.created',
      metadata: {
        model: input.model,
        timestamp: Date.now()
      }
    };

    for await (const chunk of completion) {
      const choice = chunk.choices?.[0];
      if (!choice) {continue;}

      // Handle content delta
      if (choice.delta?.content) {
        accumulatedContent += choice.delta.content;
        yield {
          type: 'response.output_text.delta',
          delta: choice.delta.content,
          content: accumulatedContent
        };
      }

      // Handle function call
      if (choice.delta?.tool_calls) {
        for (const toolCall of choice.delta.tool_calls) {
          if (toolCall.function) {
            if (!functionCall) {
              functionCall = {
                id: toolCall.id || '',
                name: toolCall.function.name || '',
                arguments: '',
                type: 'function_call',
                timestamp: Date.now()
              };
            }

            if (toolCall.function.arguments) {
              functionCall.arguments += toolCall.function.arguments;
            }
          }
        }
      }

      // Handle completion
      if (choice.finish_reason) {
        if (functionCall && functionCall.name) {
          yield {
            type: 'response.function_call_arguments.done',
            functionCall: functionCall as ResponseFunctionToolCall
          };
        }

        yield {
          type: 'response.completed',
          content: accumulatedContent,
          metadata: {
            finish_reason: choice.finish_reason,
            model: input.model,
            timestamp: Date.now()
          }
        };
        break;
      }
    }
  } catch (error) {
    yield {
      type: 'response.error',
      error: error instanceof Error ? error.message : 'Unknown error',
      metadata: {
        timestamp: Date.now()
      }
    };
  }
}

/**
 * Create response output item from completed response
 */
export function createResponseOutputItem(
  content: string,
  metadata: {
    model: string;
    provider: string;
    tokens?: number;
    thinkingTime?: number;
  }
): ResponseOutputItem {
  return {
    role: 'assistant',
    content,
    type: 'output',
    timestamp: Date.now(),
    metadata
  };
}

/**
 * Process streaming completion without tools
 */
export async function processStreamingCompletion(
  client: OpenAI,
  input: ResponseCreateInput,
  onDelta?: (delta: string) => void,
  onComplete?: (content: string) => void,
  onError?: (error: string) => void
): Promise<string> {
  try {
    const completion = await client.chat.completions.create({
      ...input,
      stream: false
    } as any);

    const choice = completion.choices?.[0];
    if (!choice) {
      throw new Error('No response choice received');
    }

    const content = choice.message?.content || '';

    onDelta?.(content);
    onComplete?.(content);

    return content;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    onError?.(errorMessage);
    throw error;
  }
}

/**
 * Process non-streaming completion
 */
export async function processCompletion(
  client: OpenAI,
  input: ResponseCreateInput
): Promise<{
  content: string;
  functionCall?: ResponseFunctionToolCall;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}> {
  try {
    const completion = await client.chat.completions.create({
      ...input,
      stream: false
    } as any);

    const choice = completion.choices?.[0];
    if (!choice) {
      throw new Error('No response choice received');
    }

    const content = choice.message?.content || '';
    let functionCall: ResponseFunctionToolCall | undefined;

    // Handle function calls
    if (choice.message?.tool_calls?.[0]) {
      const toolCall = choice.message.tool_calls[0];
      if (toolCall.type === 'function') {
        functionCall = {
          id: toolCall.id,
          name: toolCall.function.name,
          arguments: toolCall.function.arguments,
          type: 'function_call',
          timestamp: Date.now()
        };
      }
    }

    return {
      content,
      functionCall,
      usage: completion.usage
    };
  } catch (error) {
    throw error;
  }
}

/**
 * Convert messages to OpenAI format
 */
export function convertMessagesToOpenAI(
  items: ResponseItem[]
): any[] {
  const messages: any[] = [];

  for (const item of items) {
    // Handle input items (user messages)
    if (item.type === 'input' || item.type === 'message') {
      const content: Array<{ type: string; text?: string; image_url?: { url: string; detail?: string } }> = [];

      for (const contentItem of item.content) {
        if (contentItem.type === 'input_text' && contentItem.text) {
          content.push({
            type: 'text',
            text: contentItem.text
          });
        } else if (contentItem.type === 'input_image' && contentItem.image) {
          content.push({
            type: 'image_url',
            image_url: {
              url: contentItem.image.url,
              detail: contentItem.image.detail || 'auto'
            }
          });
        } else if (contentItem.type === 'image' && contentItem.image) {
          // Handle legacy image format
          content.push({
            type: 'image_url',
            image_url: {
              url: contentItem.image.url,
              detail: contentItem.image.detail || 'auto'
            }
          });
        }
      }

      // Only add message if it has content
      if (content.length > 0) {
        messages.push({
          role: item.role,
          content: content.length === 1 && content[0].type === 'text'
            ? content[0].text!
            : content
        });
      }
    } else if (item.type === 'output') {
      // Handle assistant responses
      if (item.content && item.content.trim()) {
        messages.push({
          role: 'assistant',
          content: item.content
        });
      }
    }
    // Skip function calls and tool results as they're handled separately
  }

  return messages;
}

/**
 * Estimate token count for messages
 */
export function estimateTokenCount(
  messages: Array<{
    role: string;
    content: string | Array<any>;
  }>
): number {
  let totalTokens = 0;

  for (const message of messages) {
    // Add role tokens (roughly 4 tokens per message for role and formatting)
    totalTokens += 4;

    if (typeof message.content === 'string') {
      // Rough estimation: 1 token ≈ 4 characters for English text
      totalTokens += Math.ceil(message.content.length / 4);
    } else if (Array.isArray(message.content)) {
      for (const item of message.content) {
        if (item.type === 'text' && item.text) {
          totalTokens += Math.ceil(item.text.length / 4);
        } else if (item.type === 'image_url') {
          // Images typically use 85-170 tokens depending on detail level
          totalTokens += 170; // Conservative estimate
        }
      }
    }
  }

  return totalTokens;
}

/**
 * Debug utility to inspect message conversion
 */
export function debugMessageConversion(
  items: ResponseItem[]
): {
  inputItems: number;
  outputItems: number;
  functionCalls: number;
  toolResults: number;
  convertedMessages: number;
  details: Array<{
    type: string;
    role?: string;
    hasContent: boolean;
    contentTypes: string[];
  }>;
} {
  const details: Array<{
    type: string;
    role?: string;
    hasContent: boolean;
    contentTypes: string[];
  }> = [];

  let inputItems = 0;
  let outputItems = 0;
  let functionCalls = 0;
  let toolResults = 0;

  for (const item of items) {
    const detail: any = {
      type: item.type,
      hasContent: false,
      contentTypes: []
    };

    if ('role' in item) {
      detail.role = item.role;
    }

    if (item.type === 'input' || item.type === 'message') {
      inputItems++;
      if ('content' in item && Array.isArray(item.content)) {
        detail.hasContent = item.content.length > 0;
        detail.contentTypes = item.content.map(c => c.type);
      }
    } else if (item.type === 'output') {
      outputItems++;
      detail.hasContent = !!(item as any).content && (item as any).content.trim();
    } else if (item.type === 'function_call') {
      functionCalls++;
    } else if (item.type === 'tool_result') {
      toolResults++;
    }

    details.push(detail);
  }

  const convertedMessages = convertMessagesToOpenAI(items);

  return {
    inputItems,
    outputItems,
    functionCalls,
    toolResults,
    convertedMessages: convertedMessages.length,
    details
  };
}
