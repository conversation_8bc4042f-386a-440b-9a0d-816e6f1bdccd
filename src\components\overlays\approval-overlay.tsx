/**
 * Approval Mode Selection Overlay
 * 
 * Configure approval settings and security policies
 * Provides detailed explanations for each approval mode
 */

import React, { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
import type { ApprovalPolicy } from '../../types/index.js';

interface ApprovalOverlayProps {
  currentMode: ApprovalPolicy;
  onModeChange: (mode: ApprovalPolicy) => void;
  onClose: () => void;
  visible: boolean;
}

interface ApprovalModeInfo {
  mode: ApprovalPolicy;
  title: string;
  description: string;
  details: string[];
  security: 'high' | 'medium' | 'low';
  recommended: boolean;
}

const APPROVAL_MODES: ApprovalModeInfo[] = [
  {
    mode: 'suggest',
    title: 'Suggest Mode',
    description: 'Manual approval required for all actions',
    details: [
      '• All commands require explicit user approval',
      '• Maximum security and control',
      '• Best for sensitive environments',
      '• Slower workflow but safest option',
      '• Recommended for production systems'
    ],
    security: 'high',
    recommended: true
  },
  {
    mode: 'auto-edit',
    title: 'Auto-Edit Mode',
    description: 'Automatic file edits, manual command approval',
    details: [
      '• File operations are automatically approved',
      '• System commands require manual approval',
      '• Balanced security and productivity',
      '• Good for development environments',
      '• Safe commands are pre-approved'
    ],
    security: 'medium',
    recommended: false
  },
  {
    mode: 'full-auto',
    title: 'Full-Auto Mode',
    description: 'Automatic approval for all actions (with sandbox)',
    details: [
      '• All operations are automatically approved',
      '• Commands run in sandboxed environment',
      '• Maximum productivity, reduced security',
      '• Best for isolated development',
      '• Use with caution in production'
    ],
    security: 'low',
    recommended: false
  }
];

export function ApprovalOverlay({
  currentMode,
  onModeChange,
  onClose,
  visible
}: ApprovalOverlayProps) {
  const [selectedIndex, setSelectedIndex] = useState(0);

  // Initialize selected index based on current mode
  useEffect(() => {
    const modeIndex = APPROVAL_MODES.findIndex(mode => mode.mode === currentMode);
    if (modeIndex >= 0) {
      setSelectedIndex(modeIndex);
    }
  }, [currentMode]);

  // Handle keyboard input
  useInput((input, key) => {
    if (!visible) {return;}

    // Close overlay
    if (key.escape) {
      onClose();
      return;
    }

    // Navigation
    if (key.upArrow) {
      setSelectedIndex(Math.max(0, selectedIndex - 1));
      return;
    }

    if (key.downArrow) {
      setSelectedIndex(Math.min(APPROVAL_MODES.length - 1, selectedIndex + 1));
      return;
    }

    // Selection
    if (key.return) {
      const selectedMode = APPROVAL_MODES[selectedIndex];
      onModeChange(selectedMode.mode);
      onClose();
      return;
    }

    // Quick selection by number
    if (input >= '1' && input <= '3') {
      const index = parseInt(input) - 1;
      if (index >= 0 && index < APPROVAL_MODES.length) {
        setSelectedIndex(index);
      }
      return;
    }
  });

  /**
   * Get security color
   */
  const getSecurityColor = (security: 'high' | 'medium' | 'low'): string => {
    switch (security) {
      case 'high': return 'green';
      case 'medium': return 'yellow';
      case 'low': return 'red';
    }
  };

  if (!visible) {
    return null;
  }

  const selectedMode = APPROVAL_MODES[selectedIndex];

  return (
    <Box
      borderStyle="double"
      borderColor="cyan"
      flexDirection="column"
      width="100%"
      height="100%"
    >
      {/* Header */}
      <Box paddingX={2} paddingY={1} borderStyle="single" borderColor="gray">
        <Text color="cyan" bold>
          Approval Mode Configuration
        </Text>
        <Box>
          <Text color="gray">
            Current: {currentMode} • Select security level for command execution
          </Text>
        </Box>
      </Box>

      {/* Mode List */}
      <Box paddingX={2} paddingY={1} width="50%">
        <Box flexDirection="column">
          <Box>
            <Text color="blue" bold>
              Available Modes:
            </Text>
          </Box>
          
          {APPROVAL_MODES.map((mode, index) => {
            const isSelected = index === selectedIndex;
            const isCurrent = mode.mode === currentMode;

            return (
              <Box key={mode.mode}>
                <Box width={3}>
                  <Text color={isSelected ? "black" : "gray"} backgroundColor={isSelected ? "cyan" : undefined}>
                    {index + 1}.
                  </Text>
                </Box>
                <Box flexDirection="column" flexGrow={1}>
                  <Box>
                    <Text
                      color={isSelected ? "black" : "white"}
                      backgroundColor={isSelected ? "cyan" : undefined}
                      bold={isSelected || isCurrent}
                    >
                      {mode.title}
                      {isCurrent && ' (current)'}
                      {mode.recommended && ' ⭐'}
                    </Text>
                  </Box>
                  <Box>
                    <Text
                      color={isSelected ? "black" : "gray"}
                      backgroundColor={isSelected ? "cyan" : undefined}
                    >
                      {mode.description}
                    </Text>
                  </Box>
                  <Box>
                    <Text
                      color={isSelected ? "black" : getSecurityColor(mode.security)}
                      backgroundColor={isSelected ? "cyan" : undefined}
                    >
                      Security: {mode.security.toUpperCase()}
                    </Text>
                  </Box>
                </Box>
              </Box>
            );
          })}
        </Box>
      </Box>

      {/* Mode Details */}
      <Box paddingX={2} paddingY={1} flexGrow={1}>
        <Box flexDirection="column">
          <Text color="blue" bold>
            {selectedMode.title} Details:
          </Text>
          
          <Text>
            {selectedMode.description}
          </Text>

          <Text color="blue" bold>
            Features:
          </Text>
          
          {selectedMode.details.map((detail, index) => (
            <Text key={index}>
              {detail}
            </Text>
          ))}

          <Box>
            <Text color="blue" bold>Security Level: </Text>
            <Text color={getSecurityColor(selectedMode.security)} bold>
              {selectedMode.security.toUpperCase()}
            </Text>
            {selectedMode.recommended && (
              <Text color="yellow">
                ⭐ RECOMMENDED
              </Text>
            )}
          </Box>

          {selectedMode.mode === 'suggest' && (
            <Box paddingX={1} borderStyle="single" borderColor="green">
              <Text color="green">
                💡 This is the safest option for production environments
              </Text>
            </Box>
          )}

          {selectedMode.mode === 'full-auto' && (
            <Box paddingX={1} borderStyle="single" borderColor="red">
              <Text color="red">
                ⚠️  Use with caution - commands execute automatically
              </Text>
            </Box>
          )}
        </Box>
      </Box>

      {/* Footer */}
      <Box paddingX={2} paddingY={1} borderStyle="single" borderColor="gray">
        <Text color="gray">
          ↑↓: Navigate • 1-3: Quick select • Enter: Apply • Esc: Cancel
        </Text>
      </Box>
    </Box>
  );
}
