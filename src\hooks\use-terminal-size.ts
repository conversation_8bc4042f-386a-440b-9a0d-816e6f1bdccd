/**
 * Terminal Size Management Hook
 * 
 * Provides dynamic terminal size tracking with resize event handling
 * Accounts for terminal padding and provides fallback values
 */

import { useState, useEffect } from 'react';

const TERMINAL_PADDING_X = 4; // Account for terminal padding
const TERMINAL_PADDING_Y = 2; // Account for terminal padding

interface TerminalSize {
  columns: number;
  rows: number;
  width: number;
  height: number;
}

/**
 * Custom hook for tracking terminal size
 */
export function useTerminalSize(): TerminalSize {
  const [size, setSize] = useState<TerminalSize>(() => {
    const columns = (process.stdout.columns || 80) - TERMINAL_PADDING_X;
    const rows = (process.stdout.rows || 24) - TERMINAL_PADDING_Y;
    
    return {
      columns: Math.max(columns, 40), // Minimum width
      rows: Math.max(rows, 10), // Minimum height
      width: columns,
      height: rows,
    };
  });

  useEffect(() => {
    /**
     * Handle terminal resize events
     */
    const handleResize = () => {
      const columns = (process.stdout.columns || 80) - TERMINAL_PADDING_X;
      const rows = (process.stdout.rows || 24) - TERMINAL_PADDING_Y;
      
      setSize({
        columns: Math.max(columns, 40),
        rows: Math.max(rows, 10),
        width: columns,
        height: rows,
      });
    };

    // Listen for resize events
    process.stdout.on('resize', handleResize);

    // Cleanup event listener
    return () => {
      process.stdout.off('resize', handleResize);
    };
  }, []);

  return size;
}

/**
 * Get current terminal size synchronously
 */
export function getTerminalSize(): TerminalSize {
  const columns = (process.stdout.columns || 80) - TERMINAL_PADDING_X;
  const rows = (process.stdout.rows || 24) - TERMINAL_PADDING_Y;
  
  return {
    columns: Math.max(columns, 40),
    rows: Math.max(rows, 10),
    width: columns,
    height: rows,
  };
}

/**
 * Check if terminal size is adequate for the application
 */
export function isTerminalSizeAdequate(minColumns = 60, minRows = 15): boolean {
  const { columns, rows } = getTerminalSize();
  return columns >= minColumns && rows >= minRows;
}

/**
 * Get responsive layout configuration based on terminal size
 */
export function getResponsiveLayout(): {
  isSmall: boolean;
  isMedium: boolean;
  isLarge: boolean;
  layout: 'compact' | 'normal' | 'expanded';
} {
  const { columns, rows } = getTerminalSize();
  
  const isSmall = columns < 80 || rows < 20;
  const isMedium = columns >= 80 && columns < 120 && rows >= 20;
  const isLarge = columns >= 120 && rows >= 30;
  
  let layout: 'compact' | 'normal' | 'expanded' = 'normal';
  if (isSmall) {layout = 'compact';}
  else if (isLarge) {layout = 'expanded';}
  
  return {
    isSmall,
    isMedium,
    isLarge,
    layout,
  };
}
