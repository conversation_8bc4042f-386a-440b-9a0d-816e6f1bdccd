import type { HistoryEntry } from '../../types/index.js';
export interface HistoryConfig {
    maxSize: number;
    saveHistory: boolean;
    sensitivePatterns: string[];
}
export declare function loadHistory(): HistoryEntry[];
export declare function addToHistory(command: string, success?: boolean): void;
export declare function getHistory(): HistoryEntry[];
export declare function getCommandHistory(): HistoryEntry[];
export declare function searchHistory(query: string): HistoryEntry[];
export declare function getRecentCommands(count?: number): HistoryEntry[];
export declare function getCommandAtIndex(index: number): string | null;
export declare function getHistorySize(): number;
export declare function clearHistory(): void;
export declare function getHistoryStats(): {
    totalCommands: number;
    uniqueCommands: number;
    successfulCommands: number;
    failedCommands: number;
    oldestCommand?: Date;
    newestCommand?: Date;
};
export declare function exportHistory(outputPath: string): boolean;
export declare function importHistory(inputPath: string, merge?: boolean): boolean;
export declare function updateConfig(newConfig: Partial<HistoryConfig>): void;
export declare class HistoryNavigator {
    private currentIndex;
    private history;
    constructor();
    getPrevious(): string | null;
    getNext(): string | null;
    reset(): void;
    refresh(): void;
}
//# sourceMappingURL=command-history.d.ts.map