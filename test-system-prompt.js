#!/usr/bin/env node

/**
 * Test System Prompt Functionality
 * 
 * This script tests the system prompt generation and management system
 */

import { loadConfig } from './dist/utils/config.js';
import { generateSystemPrompt, getSystemPromptManager } from './dist/utils/prompts/system-prompt-manager.js';
import { getAvailableTemplates, loadTemplate } from './dist/utils/prompts/templates/template-loader.js';
import chalk from 'chalk';

async function testSystemPrompts() {
  console.log(chalk.blue.bold('🧪 Testing System Prompt Functionality\n'));

  try {
    // Load configuration
    console.log(chalk.cyan('1. Loading configuration...'));
    const config = loadConfig();
    console.log(chalk.green('✓ Configuration loaded successfully'));
    console.log(chalk.gray(`   Provider: ${config.provider}, Model: ${config.model}\n`));

    // Test template loading
    console.log(chalk.cyan('2. Testing template loading...'));
    const availableTemplates = getAvailableTemplates();
    console.log(chalk.green(`✓ Found ${availableTemplates.length} templates:`));
    
    for (const templateId of availableTemplates) {
      try {
        const template = loadTemplate(templateId);
        console.log(chalk.gray(`   • ${template.id}: ${template.name} (${template.mode})`));
      } catch (error) {
        console.log(chalk.red(`   ✗ ${templateId}: Failed to load`));
      }
    }
    console.log();

    // Test system prompt generation
    console.log(chalk.cyan('3. Testing system prompt generation...'));
    
    const testConfigs = [
      { mode: 'general', context: 'interactive' },
      { mode: 'coding', context: 'agent-loop' },
      { mode: 'planning', context: 'single-pass' }
    ];

    for (const testConfig of testConfigs) {
      try {
        console.log(chalk.yellow(`   Testing ${testConfig.mode} mode in ${testConfig.context} context...`));
        
        const result = await generateSystemPrompt(
          testConfig.mode,
          testConfig.context,
          config,
          {
            safetyLevel: 'moderate',
            verbosityLevel: 'normal',
            enableThinking: true,
            enablePlanning: testConfig.mode === 'planning'
          }
        );

        console.log(chalk.green(`   ✓ Generated prompt (${result.prompt.length} chars, ~${result.metadata.tokenCount} tokens)`));
        console.log(chalk.gray(`     Template: ${result.metadata.templateId}`));
        
        // Show first 200 characters of the prompt
        const preview = result.prompt.substring(0, 200) + (result.prompt.length > 200 ? '...' : '');
        console.log(chalk.gray(`     Preview: ${preview.replace(/\n/g, ' ')}`));
        
      } catch (error) {
        console.log(chalk.red(`   ✗ Failed to generate ${testConfig.mode} prompt: ${error.message}`));
      }
    }
    console.log();

    // Test system prompt manager
    console.log(chalk.cyan('4. Testing system prompt manager...'));
    const manager = getSystemPromptManager(config);
    const managerTemplates = manager.getAvailableTemplates();
    console.log(chalk.green(`✓ Manager loaded with ${managerTemplates.length} templates`));
    
    // Test cache clearing
    manager.clearCache();
    console.log(chalk.green('✓ Cache cleared successfully'));
    console.log();

    // Test with custom instructions
    console.log(chalk.cyan('5. Testing custom instructions...'));
    const customResult = await generateSystemPrompt(
      'general',
      'interactive',
      config,
      {
        customInstructions: 'Always respond in a friendly and helpful manner. Focus on providing practical solutions.',
        safetyLevel: 'strict',
        verbosityLevel: 'detailed'
      }
    );
    
    console.log(chalk.green(`✓ Generated prompt with custom instructions (${customResult.prompt.length} chars)`));
    console.log(chalk.gray(`   Custom instructions included: ${customResult.prompt.includes('friendly and helpful')}`));
    console.log();

    // Test error handling
    console.log(chalk.cyan('6. Testing error handling...'));
    try {
      await generateSystemPrompt(
        'nonexistent',
        'invalid',
        config
      );
      console.log(chalk.red('✗ Should have thrown an error for invalid template'));
    } catch (error) {
      console.log(chalk.green('✓ Error handling works correctly'));
      console.log(chalk.gray(`   Error: ${error.message}`));
    }
    console.log();

    console.log(chalk.green.bold('🎉 All system prompt tests completed successfully!'));
    
  } catch (error) {
    console.error(chalk.red.bold('❌ Test failed:'), error.message);
    if (process.env.DEBUG) {
      console.error(chalk.gray(error.stack));
    }
    process.exit(1);
  }
}

// Run tests
testSystemPrompts().catch(error => {
  console.error(chalk.red('Fatal error:'), error.message);
  process.exit(1);
});
