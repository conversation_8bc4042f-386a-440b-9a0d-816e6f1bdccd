/**
 * Git Repository Detection
 * 
 * Provides reliable Git repository detection using git commands
 * Fast synchronous operation that works across different Git versions
 */

import { execSync } from 'child_process';
import { existsSync } from 'fs';
import { join } from 'path';

/**
 * Check if current directory is inside a Git repository
 */
export function checkInGit(workdir: string = process.cwd()): boolean {
  try {
    // Method 1: Use git rev-parse command (most reliable)
    execSync('git rev-parse --is-inside-work-tree', {
      cwd: workdir,
      stdio: 'ignore',
      timeout: 5000
    });
    return true;
  } catch {
    // Method 2: Check for .git directory (fallback)
    return checkGitDirectory(workdir);
  }
}

/**
 * Check for .git directory in current or parent directories
 */
function checkGitDirectory(startDir: string): boolean {
  let currentDir = startDir;
  
  // Walk up the directory tree looking for .git
  while (currentDir !== '/' && currentDir !== '' && currentDir.length > 1) {
    const gitPath = join(currentDir, '.git');
    
    if (existsSync(gitPath)) {
      return true;
    }
    
    // Move to parent directory
    const parentDir = join(currentDir, '..');
    if (parentDir === currentDir) {
      break; // Reached root
    }
    currentDir = parentDir;
  }
  
  return false;
}

/**
 * Get Git repository root directory
 */
export function getGitRoot(workdir: string = process.cwd()): string | null {
  try {
    const result = execSync('git rev-parse --show-toplevel', {
      cwd: workdir,
      encoding: 'utf-8',
      timeout: 5000
    });
    return result.trim();
  } catch {
    return null;
  }
}

/**
 * Get current Git branch name
 */
export function getCurrentBranch(workdir: string = process.cwd()): string | null {
  try {
    const result = execSync('git rev-parse --abbrev-ref HEAD', {
      cwd: workdir,
      encoding: 'utf-8',
      timeout: 5000
    });
    return result.trim();
  } catch {
    return null;
  }
}

/**
 * Check if Git repository has uncommitted changes
 */
export function hasUncommittedChanges(workdir: string = process.cwd()): boolean {
  try {
    const result = execSync('git status --porcelain', {
      cwd: workdir,
      encoding: 'utf-8',
      timeout: 5000
    });
    return result.trim().length > 0;
  } catch {
    return false;
  }
}

/**
 * Get Git repository status information
 */
export function getGitStatus(workdir: string = process.cwd()): {
  inGit: boolean;
  root?: string;
  branch?: string;
  hasChanges?: boolean;
  isClean?: boolean;
} {
  const inGit = checkInGit(workdir);
  
  if (!inGit) {
    return { inGit: false };
  }
  
  const root = getGitRoot(workdir);
  const branch = getCurrentBranch(workdir);
  const hasChanges = hasUncommittedChanges(workdir);
  
  return {
    inGit: true,
    root: root || undefined,
    branch: branch || undefined,
    hasChanges,
    isClean: !hasChanges
  };
}

/**
 * Check if file is tracked by Git
 */
export function isFileTracked(filePath: string, workdir: string = process.cwd()): boolean {
  try {
    execSync(`git ls-files --error-unmatch "${filePath}"`, {
      cwd: workdir,
      stdio: 'ignore',
      timeout: 5000
    });
    return true;
  } catch {
    return false;
  }
}

/**
 * Get list of modified files
 */
export function getModifiedFiles(workdir: string = process.cwd()): string[] {
  try {
    const result = execSync('git diff --name-only', {
      cwd: workdir,
      encoding: 'utf-8',
      timeout: 5000
    });
    
    return result
      .trim()
      .split('\n')
      .filter(line => line.length > 0);
  } catch {
    return [];
  }
}

/**
 * Get list of staged files
 */
export function getStagedFiles(workdir: string = process.cwd()): string[] {
  try {
    const result = execSync('git diff --cached --name-only', {
      cwd: workdir,
      encoding: 'utf-8',
      timeout: 5000
    });
    
    return result
      .trim()
      .split('\n')
      .filter(line => line.length > 0);
  } catch {
    return [];
  }
}

/**
 * Get list of untracked files
 */
export function getUntrackedFiles(workdir: string = process.cwd()): string[] {
  try {
    const result = execSync('git ls-files --others --exclude-standard', {
      cwd: workdir,
      encoding: 'utf-8',
      timeout: 5000
    });
    
    return result
      .trim()
      .split('\n')
      .filter(line => line.length > 0);
  } catch {
    return [];
  }
}

/**
 * Get comprehensive Git file status
 */
export function getFileStatus(workdir: string = process.cwd()): {
  modified: string[];
  staged: string[];
  untracked: string[];
  total: number;
} {
  const modified = getModifiedFiles(workdir);
  const staged = getStagedFiles(workdir);
  const untracked = getUntrackedFiles(workdir);
  
  return {
    modified,
    staged,
    untracked,
    total: modified.length + staged.length + untracked.length
  };
}
