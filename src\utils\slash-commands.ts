/**
 * Slash Command System
 * 
 * Provides built-in commands for the CLI interface
 * Supports auto-completion and parameter handling
 */

export interface SlashCommand {
  command: string;
  description: string;
  parameters?: string[];
  examples?: string[];
  handler?: (args: string[]) => Promise<string> | string;
}

/**
 * Available slash commands
 */
export const SLASH_COMMANDS: SlashCommand[] = [
  {
    command: "/help",
    description: "Show help information and available commands",
    examples: ["/help", "/help model"],
    handler: async (args: string[]) => {
      if (args.length > 0) {
        const topic = args[0].toLowerCase();
        return getHelpForTopic(topic);
      }
      return getGeneralHelp();
    }
  },
  {
    command: "/clear",
    description: "Clear conversation history",
    examples: ["/clear"],
    handler: async () => {
      return "Conversation history cleared.";
    }
  },
  {
    command: "/compact",
    description: "Compress conversation context to save tokens",
    examples: ["/compact"],
    handler: async () => {
      return "Conversation context compressed.";
    }
  },
  {
    command: "/history",
    description: "Open command history browser",
    examples: ["/history"],
    handler: async () => {
      return "Opening command history...";
    }
  },
  {
    command: "/sessions",
    description: "Browse and manage previous sessions",
    examples: ["/sessions", "/sessions list", "/sessions load <id>"],
    parameters: ["action", "id"],
    handler: async (args: string[]) => {
      const action = args[0] || "list";
      switch (action) {
        case "list":
          return "Listing available sessions...";
        case "load": {
          const sessionId = args[1];
          if (!sessionId) {
            return "Error: Session ID required. Usage: /sessions load <id>";
          }
          return `Loading session: ${sessionId}`;
        }
        case "delete": {
          const deleteId = args[1];
          if (!deleteId) {
            return "Error: Session ID required. Usage: /sessions delete <id>";
          }
          return `Deleting session: ${deleteId}`;
        }
        default:
          return `Unknown sessions action: ${action}. Available: list, load, delete`;
      }
    }
  },
  {
    command: "/model",
    description: "Open model and provider selection panel",
    examples: ["/model", "/model list", "/model set gpt-4"],
    parameters: ["action", "model"],
    handler: async (args: string[]) => {
      const action = args[0] || "select";
      switch (action) {
        case "select":
          return "Opening model selection panel...";
        case "list":
          return "Listing available models...";
        case "set": {
          const model = args[1];
          if (!model) {
            return "Error: Model name required. Usage: /model set <model>";
          }
          return `Setting model to: ${model}`;
        }
        default:
          return `Unknown model action: ${action}. Available: select, list, set`;
      }
    }
  },
  {
    command: "/approval",
    description: "Open approval mode selection panel",
    examples: ["/approval", "/approval suggest", "/approval auto-edit", "/approval full-auto"],
    parameters: ["mode"],
    handler: async (args: string[]) => {
      if (args.length === 0) {
        return "Opening approval mode selection...";
      }
      
      const mode = args[0];
      const validModes = ["suggest", "auto-edit", "full-auto"];
      
      if (!validModes.includes(mode)) {
        return `Invalid approval mode: ${mode}. Valid modes: ${validModes.join(", ")}`;
      }
      
      return `Setting approval mode to: ${mode}`;
    }
  },
  {
    command: "/bug",
    description: "Generate bug report URL for GitHub issues",
    examples: ["/bug"],
    handler: async () => {
      return "Generating bug report URL...";
    }
  },
  {
    command: "/diff",
    description: "Show git diff of current changes",
    examples: ["/diff", "/diff --cached", "/diff HEAD~1"],
    parameters: ["options"],
    handler: async (args: string[]) => {
      const options = args.join(" ");
      return `Showing git diff${options ? ` with options: ${options}` : ""}...`;
    }
  },
  {
    command: "/config",
    description: "View or modify configuration settings",
    examples: ["/config", "/config show", "/config set model gpt-4"],
    parameters: ["action", "key", "value"],
    handler: async (args: string[]) => {
      const action = args[0] || "show";
      switch (action) {
        case "show":
          return "Showing current configuration...";
        case "set": {
          const key = args[1];
          const value = args[2];
          if (!key || !value) {
            return "Error: Key and value required. Usage: /config set <key> <value>";
          }
          return `Setting ${key} to ${value}`;
        }
        case "reset":
          return "Resetting configuration to defaults...";
        default:
          return `Unknown config action: ${action}. Available: show, set, reset`;
      }
    }
  },
  {
    command: "/version",
    description: "Show version information",
    examples: ["/version"],
    handler: async () => {
      return "Showing version information...";
    }
  },
  {
    command: "/exit",
    description: "Exit the application",
    examples: ["/exit", "/quit"],
    handler: async () => {
      return "Exiting application...";
    }
  },
  {
    command: "/quit",
    description: "Exit the application (alias for /exit)",
    examples: ["/quit"],
    handler: async () => {
      return "Exiting application...";
    }
  }
];

/**
 * Find slash command by name
 */
export function findSlashCommand(commandName: string): SlashCommand | null {
  const name = commandName.toLowerCase();
  return SLASH_COMMANDS.find(cmd => cmd.command.toLowerCase() === name) || null;
}

/**
 * Get all slash command names for auto-completion
 */
export function getSlashCommandNames(): string[] {
  return SLASH_COMMANDS.map(cmd => cmd.command);
}

/**
 * Check if text starts with a slash command
 */
export function isSlashCommand(text: string): boolean {
  const trimmed = text.trim();
  return trimmed.startsWith('/') && trimmed.length > 1;
}

/**
 * Parse slash command from text
 */
export function parseSlashCommand(text: string): {
  command: string;
  args: string[];
  isValid: boolean;
} {
  const trimmed = text.trim();
  
  if (!isSlashCommand(trimmed)) {
    return { command: "", args: [], isValid: false };
  }

  const parts = trimmed.split(/\s+/);
  const command = parts[0];
  const args = parts.slice(1);

  const slashCommand = findSlashCommand(command);
  
  return {
    command,
    args,
    isValid: !!slashCommand
  };
}

/**
 * Execute slash command
 */
export async function executeSlashCommand(text: string): Promise<{
  success: boolean;
  result: string;
  command?: string;
}> {
  const parsed = parseSlashCommand(text);
  
  if (!parsed.isValid) {
    return {
      success: false,
      result: `Unknown command: ${parsed.command}. Type /help for available commands.`
    };
  }

  const slashCommand = findSlashCommand(parsed.command);
  if (!slashCommand || !slashCommand.handler) {
    return {
      success: false,
      result: `Command ${parsed.command} is not implemented yet.`
    };
  }

  try {
    const result = await slashCommand.handler(parsed.args);
    return {
      success: true,
      result,
      command: parsed.command
    };
  } catch (error) {
    return {
      success: false,
      result: `Error executing ${parsed.command}: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Get auto-completion suggestions for slash commands
 */
export function getSlashCommandSuggestions(input: string): string[] {
  const trimmed = input.trim().toLowerCase();
  
  if (!trimmed.startsWith('/')) {
    return [];
  }

  const commandNames = getSlashCommandNames();
  
  if (trimmed === '/') {
    return commandNames;
  }

  return commandNames.filter(name => 
    name.toLowerCase().startsWith(trimmed)
  );
}

/**
 * Get general help text
 */
function getGeneralHelp(): string {
  const helpText = [
    "Kritrima AI CLI - Available Commands:",
    "",
    ...SLASH_COMMANDS.map(cmd => `  ${cmd.command.padEnd(12)} - ${cmd.description}`),
    "",
    "Type /help <command> for detailed information about a specific command.",
    "Use @ to reference files (e.g., @file.txt) and they will be included in context.",
    "",
    "Keyboard shortcuts:",
    "  Ctrl+C     - Exit application",
    "  Escape     - Close overlays",
    "  Tab        - Auto-complete commands and file paths",
    "  Up/Down    - Navigate command history",
    "  Ctrl+L     - Clear screen"
  ];
  
  return helpText.join('\n');
}

/**
 * Get help for specific topic
 */
function getHelpForTopic(topic: string): string {
  const command = findSlashCommand(`/${topic}`);
  
  if (!command) {
    return `No help available for topic: ${topic}`;
  }

  const helpText = [
    `Command: ${command.command}`,
    `Description: ${command.description}`,
    ""
  ];

  if (command.parameters && command.parameters.length > 0) {
    helpText.push(`Parameters: ${command.parameters.join(", ")}`);
    helpText.push("");
  }

  if (command.examples && command.examples.length > 0) {
    helpText.push("Examples:");
    command.examples.forEach(example => {
      helpText.push(`  ${example}`);
    });
  }

  return helpText.join('\n');
}
