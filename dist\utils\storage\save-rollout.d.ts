import type { ResponseItem, SessionData, SessionMetadata } from '../../types/index.js';
export declare function getSessionsDir(): string;
export declare function saveRollout(sessionId: string, items: ResponseItem[], config?: any): void;
export declare function saveRolloutAsync(sessionId: string, items: ResponseItem[], config?: any): Promise<void>;
export declare function loadSession(sessionId: string): SessionData | null;
export declare function getSavedSessions(): Promise<SessionMetadata[]>;
export declare function listSessions(): SessionMetadata[];
export declare function deleteSession(sessionId: string): boolean;
export declare function getSessionStats(): {
    totalSessions: number;
    totalSize: number;
    oldestSession?: Date;
    newestSession?: Date;
};
export declare function exportSession(sessionId: string, outputPath: string): boolean;
export declare function importSession(inputPath: string): Promise<string | null>;
export declare function formatFileSize(bytes: number): string;
//# sourceMappingURL=save-rollout.d.ts.map