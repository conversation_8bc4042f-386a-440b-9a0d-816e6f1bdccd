/**
 * Agent State Management Hook
 * 
 * Provides centralized state management for agent operations
 * Handles execution state, progress tracking, and error management
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import type { AgentState, AgentOperation, AgentError } from '../types/index.js';

export interface UseAgentStateOptions {
  onStateChange?: (state: AgentState) => void;
  onError?: (error: AgentError) => void;
  onComplete?: (result: any) => void;
}

export interface AgentStateManager {
  state: AgentState;
  isIdle: boolean;
  isThinking: boolean;
  isExecuting: boolean;
  isStreaming: boolean;
  hasError: boolean;
  currentOperation: AgentOperation | null;
  error: AgentError | null;
  progress: number;
  
  // State transitions
  startThinking: (operation?: AgentOperation) => void;
  startExecuting: (operation: AgentOperation) => void;
  startStreaming: (operation?: AgentOperation) => void;
  setProgress: (progress: number) => void;
  setError: (error: AgentError) => void;
  complete: (result?: any) => void;
  reset: () => void;
  
  // Operation management
  updateOperation: (updates: Partial<AgentOperation>) => void;
  addStep: (step: string) => void;
  completeStep: (stepIndex: number) => void;
}

export function useAgentState(options: UseAgentStateOptions = {}): AgentStateManager {
  const [state, setState] = useState<AgentState>('idle');
  const [currentOperation, setCurrentOperation] = useState<AgentOperation | null>(null);
  const [error, setError] = useState<AgentError | null>(null);
  const [progress, setProgress] = useState(0);
  
  const optionsRef = useRef(options);
  optionsRef.current = options;

  // Derived state
  const isIdle = state === 'idle';
  const isThinking = state === 'thinking';
  const isExecuting = state === 'executing';
  const isStreaming = state === 'streaming';
  const hasError = state === 'error';

  // State change handler
  useEffect(() => {
    optionsRef.current.onStateChange?.(state);
  }, [state]);

  // Error handler
  useEffect(() => {
    if (error) {
      optionsRef.current.onError?.(error);
    }
  }, [error]);

  // State transition functions
  const startThinking = useCallback((operation?: AgentOperation) => {
    setState('thinking');
    setError(null);
    setProgress(0);
    
    if (operation) {
      setCurrentOperation(operation);
    }
  }, []);

  const startExecuting = useCallback((operation: AgentOperation) => {
    setState('executing');
    setError(null);
    setProgress(0);
    setCurrentOperation(operation);
  }, []);

  const startStreaming = useCallback((operation?: AgentOperation) => {
    setState('streaming');
    setError(null);
    setProgress(0);
    
    if (operation) {
      setCurrentOperation(operation);
    }
  }, []);

  const setProgressValue = useCallback((newProgress: number) => {
    setProgress(Math.max(0, Math.min(100, newProgress)));
  }, []);

  const setErrorValue = useCallback((newError: AgentError) => {
    setState('error');
    setError(newError);
    setProgress(0);
  }, []);

  const complete = useCallback((result?: any) => {
    setState('idle');
    setError(null);
    setProgress(100);
    
    // Keep operation for a moment to show completion
    setTimeout(() => {
      setCurrentOperation(null);
      setProgress(0);
    }, 1000);

    optionsRef.current.onComplete?.(result);
  }, []);

  const reset = useCallback(() => {
    setState('idle');
    setCurrentOperation(null);
    setError(null);
    setProgress(0);
  }, []);

  // Operation management
  const updateOperation = useCallback((updates: Partial<AgentOperation>) => {
    setCurrentOperation(prev => prev ? { ...prev, ...updates } : null);
  }, []);

  const addStep = useCallback((step: string) => {
    setCurrentOperation(prev => {
      if (!prev) {return null;}
      
      return {
        ...prev,
        steps: [...(prev.steps || []), {
          id: `step-${Date.now()}`,
          description: step,
          status: 'pending',
          timestamp: Date.now()
        }]
      };
    });
  }, []);

  const completeStep = useCallback((stepIndex: number) => {
    setCurrentOperation(prev => {
      if (!prev || !prev.steps) {return prev;}
      
      const updatedSteps = [...prev.steps];
      if (updatedSteps[stepIndex]) {
        updatedSteps[stepIndex] = {
          ...updatedSteps[stepIndex],
          status: 'completed',
          completedAt: Date.now()
        };
      }
      
      return {
        ...prev,
        steps: updatedSteps
      };
    });
  }, []);

  return {
    state,
    isIdle,
    isThinking,
    isExecuting,
    isStreaming,
    hasError,
    currentOperation,
    error,
    progress,
    
    startThinking,
    startExecuting,
    startStreaming,
    setProgress: setProgressValue,
    setError: setErrorValue,
    complete,
    reset,
    
    updateOperation,
    addStep,
    completeStep
  };
}

/**
 * Hook for managing multiple agent operations
 */
export interface UseAgentQueueOptions {
  maxConcurrent?: number;
  onQueueChange?: (queue: AgentOperation[]) => void;
}

export function useAgentQueue(options: UseAgentQueueOptions = {}) {
  const [queue, setQueue] = useState<AgentOperation[]>([]);
  const [active, setActive] = useState<AgentOperation[]>([]);
  const [completed, setCompleted] = useState<AgentOperation[]>([]);
  
  const maxConcurrent = options.maxConcurrent || 1;

  // Queue management
  const addToQueue = useCallback((operation: AgentOperation) => {
    setQueue(prev => [...prev, operation]);
  }, []);

  const removeFromQueue = useCallback((operationId: string) => {
    setQueue(prev => prev.filter(op => op.id !== operationId));
  }, []);

  const startNext = useCallback(() => {
    if (active.length >= maxConcurrent || queue.length === 0) {
      return null;
    }

    const nextOperation = queue[0];
    setQueue(prev => prev.slice(1));
    setActive(prev => [...prev, nextOperation]);
    
    return nextOperation;
  }, [queue, active.length, maxConcurrent]);

  const completeOperation = useCallback((operationId: string, result?: any) => {
    setActive(prev => {
      const operation = prev.find(op => op.id === operationId);
      if (operation) {
        setCompleted(completedPrev => [...completedPrev, {
          ...operation,
          status: 'completed',
          result,
          completedAt: Date.now()
        }]);
      }
      return prev.filter(op => op.id !== operationId);
    });
  }, []);

  const failOperation = useCallback((operationId: string, error: AgentError) => {
    setActive(prev => {
      const operation = prev.find(op => op.id === operationId);
      if (operation) {
        setCompleted(completedPrev => [...completedPrev, {
          ...operation,
          status: 'failed',
          error,
          completedAt: Date.now()
        }]);
      }
      return prev.filter(op => op.id !== operationId);
    });
  }, []);

  const clearCompleted = useCallback(() => {
    setCompleted([]);
  }, []);

  const clearAll = useCallback(() => {
    setQueue([]);
    setActive([]);
    setCompleted([]);
  }, []);

  // Auto-start next operation when possible
  useEffect(() => {
    if (active.length < maxConcurrent && queue.length > 0) {
      const timer = setTimeout(startNext, 0);
      return () => clearTimeout(timer);
    }
  }, [active.length, queue.length, maxConcurrent, startNext]);

  // Notify queue changes
  useEffect(() => {
    options.onQueueChange?.(queue);
  }, [queue, options]);

  return {
    queue,
    active,
    completed,
    
    addToQueue,
    removeFromQueue,
    startNext,
    completeOperation,
    failOperation,
    clearCompleted,
    clearAll,
    
    // Computed properties
    totalPending: queue.length,
    totalActive: active.length,
    totalCompleted: completed.length,
    canStartNext: active.length < maxConcurrent && queue.length > 0
  };
}

/**
 * Hook for tracking agent performance metrics
 */
export function useAgentMetrics() {
  const [metrics, setMetrics] = useState({
    totalOperations: 0,
    successfulOperations: 0,
    failedOperations: 0,
    averageExecutionTime: 0,
    totalExecutionTime: 0,
    operationsPerMinute: 0,
    lastOperationTime: 0
  });

  const recordOperation = useCallback((
    success: boolean,
    executionTime: number
  ) => {
    setMetrics(prev => {
      const newTotal = prev.totalOperations + 1;
      const newSuccessful = success ? prev.successfulOperations + 1 : prev.successfulOperations;
      const newFailed = success ? prev.failedOperations : prev.failedOperations + 1;
      const newTotalTime = prev.totalExecutionTime + executionTime;
      const newAverageTime = newTotalTime / newTotal;
      
      // Calculate operations per minute (simple sliding window)
      const now = Date.now();
      const timeSinceStart = now - (prev.lastOperationTime || now);
      const opsPerMinute = timeSinceStart > 0 ? (newTotal / timeSinceStart) * 60000 : 0;

      return {
        totalOperations: newTotal,
        successfulOperations: newSuccessful,
        failedOperations: newFailed,
        averageExecutionTime: newAverageTime,
        totalExecutionTime: newTotalTime,
        operationsPerMinute: opsPerMinute,
        lastOperationTime: now
      };
    });
  }, []);

  const resetMetrics = useCallback(() => {
    setMetrics({
      totalOperations: 0,
      successfulOperations: 0,
      failedOperations: 0,
      averageExecutionTime: 0,
      totalExecutionTime: 0,
      operationsPerMinute: 0,
      lastOperationTime: 0
    });
  }, []);

  return {
    metrics,
    recordOperation,
    resetMetrics,
    
    // Computed properties
    successRate: metrics.totalOperations > 0 ? 
      (metrics.successfulOperations / metrics.totalOperations) * 100 : 0,
    failureRate: metrics.totalOperations > 0 ? 
      (metrics.failedOperations / metrics.totalOperations) * 100 : 0
  };
}
