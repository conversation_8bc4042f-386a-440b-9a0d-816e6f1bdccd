export interface ProviderConfig {
    name: string;
    baseURL: string;
    envKey: string;
}
export type ProviderName = 'openai' | 'azure' | 'gemini' | 'ollama' | 'mistral' | 'deepseek' | 'xai' | 'groq' | 'arceeai' | 'openrouter';
export interface ModelInfo {
    id: string;
    name: string;
    contextLength: number;
    supportsImages: boolean;
    supportsTools: boolean;
    provider: ProviderName;
}
export type ApprovalPolicy = 'suggest' | 'auto-edit' | 'full-auto';
export interface AppConfig {
    model: string;
    provider: ProviderName;
    approvalMode: ApprovalPolicy;
    providers?: Record<string, ProviderConfig>;
    safeCommands?: string[];
    dangerousCommands?: string[];
    maxTokens?: number;
    temperature?: number;
    timeout?: number;
    enableNotifications?: boolean;
    enableLogging?: boolean;
    projectDocPath?: string;
    additionalWritableRoots?: string[];
    telemetryEnabled?: boolean;
    useSandbox?: boolean;
    debug?: boolean;
    logLevel?: 'debug' | 'info' | 'warn' | 'error';
    systemPromptMode?: SystemPromptMode;
    systemPromptContext?: SystemPromptContextType;
    customInstructions?: string;
    safetyLevel?: 'strict' | 'moderate' | 'permissive';
    verbosityLevel?: 'minimal' | 'normal' | 'detailed' | 'verbose';
}
export interface ResponseContentInput {
    type: 'input_text' | 'input_image' | 'image';
    text?: string;
    image?: {
        url: string;
        detail?: 'low' | 'high' | 'auto';
    };
}
export interface ResponseInputItem {
    role: 'user' | 'assistant' | 'system';
    content: ResponseContentInput[];
    type: 'message' | 'input';
    timestamp?: number;
}
export interface ResponseOutputItem {
    role?: 'assistant';
    content: string;
    type: 'output';
    timestamp?: number;
    metadata?: {
        model?: string;
        provider?: string;
        tokens?: number;
        thinkingTime?: number;
    };
}
export interface ResponseFunctionToolCall {
    id: string;
    name: string;
    arguments: string;
    type: 'function_call';
    timestamp?: number;
    role?: 'assistant';
}
export interface ResponseToolResult {
    id: string;
    result: string;
    success: boolean;
    type: 'tool_result';
    timestamp?: number;
    role?: 'assistant';
    metadata?: {
        command?: string[];
        workdir?: string;
        exitCode?: number;
        duration?: number;
    };
}
export type ResponseItem = ResponseInputItem | ResponseOutputItem | ResponseFunctionToolCall | ResponseToolResult;
export interface FunctionTool {
    type: 'function';
    function: {
        name: string;
        description: string;
        parameters: {
            type: 'object';
            properties: Record<string, any>;
            required: string[];
        };
    };
}
export interface ExecInput {
    command: string[];
    workdir?: string;
    timeout?: number;
}
export interface ExecResult {
    success: boolean;
    output: string;
    error?: string;
    exitCode: number;
    duration: number;
    command: string[];
    workdir: string;
}
export type OverlayModeType = 'none' | 'history' | 'sessions' | 'model' | 'approval' | 'help' | 'diff';
export interface ConfirmationResult {
    decision: 'yes' | 'no_continue' | 'no_exit' | 'always' | 'explain';
    customMessage?: string;
}
export interface HistoryEntry {
    command: string;
    timestamp: number;
    success?: boolean;
}
export interface SessionMetadata {
    id: string;
    timestamp: number;
    model: string;
    provider: string;
    approvalMode?: ApprovalPolicy;
    itemCount: number;
    lastActivity: number;
}
export interface SessionData {
    metadata: SessionMetadata;
    items: ResponseItem[];
    config: Partial<AppConfig>;
}
export declare class KritrimaError extends Error {
    code: string;
    details?: any | undefined;
    constructor(message: string, code: string, details?: any | undefined);
}
export declare class NetworkError extends KritrimaError {
    constructor(message: string, details?: any);
}
export declare class ConfigError extends KritrimaError {
    constructor(message: string, details?: any);
}
export declare class SecurityError extends KritrimaError {
    constructor(message: string, details?: any);
}
export type AgentState = 'idle' | 'thinking' | 'executing' | 'streaming' | 'error';
export interface AgentStep {
    id: string;
    description: string;
    status: 'pending' | 'running' | 'completed' | 'failed';
    timestamp: number;
    completedAt?: number;
    error?: string;
}
export interface AgentOperation {
    id: string;
    type: 'command' | 'file_operation' | 'analysis' | 'generation';
    description: string;
    status: 'pending' | 'running' | 'completed' | 'failed';
    timestamp: number;
    completedAt?: number;
    steps?: AgentStep[];
    result?: any;
    error?: AgentError;
    metadata?: Record<string, any>;
}
export interface AgentError {
    message: string;
    code?: string;
    stack?: string;
    context?: Record<string, any>;
    recoverable?: boolean;
}
export type SystemPromptMode = 'general' | 'coding' | 'analysis' | 'planning' | 'execution' | 'validation' | 'debugging' | 'documentation' | 'testing';
export type SystemPromptContextType = 'cli' | 'interactive' | 'single-pass' | 'agent-loop' | 'batch' | 'streaming';
export interface SystemPromptConfig {
    mode: SystemPromptMode;
    context: SystemPromptContextType;
    provider: ProviderName;
    model: string;
    includeSystemInfo?: boolean;
    includeProjectContext?: boolean;
    includeToolContext?: boolean;
    includeSessionContext?: boolean;
    customInstructions?: string;
    temperature?: number;
    maxTokens?: number;
    enableThinking?: boolean;
    enablePlanning?: boolean;
    enableValidation?: boolean;
    safetyLevel?: 'strict' | 'moderate' | 'permissive';
    verbosityLevel?: 'minimal' | 'normal' | 'detailed' | 'verbose';
}
export interface SystemPromptTemplate {
    id: string;
    name: string;
    description: string;
    mode: SystemPromptMode;
    context: SystemPromptContextType[];
    template: string;
    variables: Record<string, SystemPromptVariable>;
    metadata: {
        version: string;
        author: string;
        created: number;
        updated: number;
        tags: string[];
    };
}
export interface SystemPromptVariable {
    name: string;
    type: 'string' | 'number' | 'boolean' | 'array' | 'object';
    description: string;
    required: boolean;
    defaultValue?: any;
    validation?: {
        pattern?: string;
        min?: number;
        max?: number;
        enum?: any[];
    };
}
export interface SystemPromptContextData {
    system: {
        os: string;
        platform: string;
        nodeVersion: string;
        workingDirectory: string;
        timestamp: number;
    };
    project?: {
        name?: string;
        type?: string;
        language?: string;
        framework?: string;
        dependencies?: string[];
        structure?: string;
    };
    session: {
        id: string;
        startTime: number;
        messageCount: number;
        lastActivity: number;
        preferences: Record<string, any>;
    };
    tools: {
        available: string[];
        permissions: Record<string, boolean>;
        restrictions: string[];
    };
    user?: {
        preferences?: Record<string, any>;
        customInstructions?: string;
        expertise?: string[];
    };
}
export interface SystemPromptResult {
    prompt: string;
    metadata: {
        templateId: string;
        mode: SystemPromptMode;
        context: SystemPromptContextType;
        variables: Record<string, any>;
        generatedAt: number;
        tokenCount?: number;
    };
}
export interface TextContent {
    type: 'input_text';
    text: string;
}
export interface ImageContent {
    type: 'image';
    source: {
        type: 'base64';
        media_type: string;
        data: string;
    };
}
export type MessageContent = TextContent | ImageContent;
export interface InputContent {
    type: 'text' | 'file' | 'image' | 'url';
    content: string;
    metadata?: Record<string, any>;
}
//# sourceMappingURL=index.d.ts.map