/**
 * File Tag Expansion System
 * 
 * Handles conversion between @file.txt tags and XML blocks
 * Provides bidirectional transformation for file content inclusion
 */

import { readFileSync, existsSync, statSync } from 'fs';
import { extname, basename, resolve } from 'path';

// Maximum file size to include (1MB)
const MAX_FILE_SIZE = 1024 * 1024;

// File extensions to treat as text
const TEXT_EXTENSIONS = [
  '.txt', '.md', '.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.c', '.cpp', '.h', '.hpp',
  '.css', '.scss', '.sass', '.less', '.html', '.htm', '.xml', '.json', '.yaml', '.yml',
  '.toml', '.ini', '.cfg', '.conf', '.sh', '.bash', '.zsh', '.fish', '.ps1', '.bat', '.cmd',
  '.sql', '.graphql', '.gql', '.proto', '.dockerfile', '.makefile', '.cmake', '.gradle',
  '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala', '.clj', '.hs', '.elm', '.vue',
  '.svelte', '.astro', '.r', '.m', '.pl', '.lua', '.dart', '.nim', '.zig', '.v'
];

/**
 * Expand @file.txt tags to XML blocks with file contents
 */
export async function expandFileTags(text: string, workingDir: string = process.cwd()): Promise<string> {
  // Pattern to match @file.ext tags
  const fileTagPattern = /@([^\s@]+)/g;
  
  let expandedText = text;
  const matches = Array.from(text.matchAll(fileTagPattern));
  
  for (const match of matches) {
    const filePath = match[1];
    const fullMatch = match[0];
    
    try {
      const xmlBlock = await createXmlBlock(filePath, workingDir);
      expandedText = expandedText.replace(fullMatch, xmlBlock);
    } catch (error) {
      // Replace with error message
      const errorBlock = createErrorBlock(filePath, error instanceof Error ? error.message : 'Unknown error');
      expandedText = expandedText.replace(fullMatch, errorBlock);
    }
  }
  
  return expandedText;
}

/**
 * Collapse XML blocks back to @file.txt format
 */
export function collapseXmlBlocks(text: string): string {
  // Pattern to match XML file blocks
  const xmlBlockPattern = /<file path="([^"]+)"[^>]*>[\s\S]*?<\/file>/g;
  
  let collapsedText = text;
  const matches = Array.from(text.matchAll(xmlBlockPattern));
  
  for (const match of matches) {
    const filePath = match[1];
    const fullMatch = match[0];
    const fileTag = `@${filePath}`;
    
    collapsedText = collapsedText.replace(fullMatch, fileTag);
  }
  
  return collapsedText;
}

/**
 * Create XML block for file content
 */
async function createXmlBlock(filePath: string, workingDir: string): Promise<string> {
  const resolvedPath = resolve(workingDir, filePath);
  
  // Check if file exists
  if (!existsSync(resolvedPath)) {
    throw new Error(`File not found: ${filePath}`);
  }
  
  // Get file stats
  const stats = statSync(resolvedPath);
  
  if (!stats.isFile()) {
    throw new Error(`Path is not a file: ${filePath}`);
  }
  
  // Check file size
  if (stats.size > MAX_FILE_SIZE) {
    throw new Error(`File too large: ${filePath} (${formatFileSize(stats.size)}). Maximum size: ${formatFileSize(MAX_FILE_SIZE)}`);
  }
  
  // Determine if file is text
  const isTextFile = isTextFileType(filePath);
  
  if (!isTextFile) {
    return createBinaryFileBlock(filePath, stats.size);
  }
  
  // Read file content
  let content: string;
  try {
    content = readFileSync(resolvedPath, 'utf-8');
  } catch {
    throw new Error(`Could not read file: ${filePath}`);
  }
  
  // Create XML block
  const fileName = basename(filePath);
  const fileExt = extname(filePath).slice(1); // Remove leading dot
  
  return [
    `<file path="${filePath}" name="${fileName}" size="${stats.size}" type="text"${fileExt ? ` language="${fileExt}"` : ''}>`,
    content,
    '</file>'
  ].join('\n');
}

/**
 * Create XML block for binary files
 */
function createBinaryFileBlock(filePath: string, size: number): string {
  const fileName = basename(filePath);
  const fileExt = extname(filePath).slice(1);
  
  return [
    `<file path="${filePath}" name="${fileName}" size="${size}" type="binary"${fileExt ? ` extension="${fileExt}"` : ''}>`,
    `[Binary file: ${fileName} (${formatFileSize(size)})]`,
    '</file>'
  ].join('\n');
}

/**
 * Create error block for failed file reads
 */
function createErrorBlock(filePath: string, error: string): string {
  const fileName = basename(filePath);
  
  return [
    `<file path="${filePath}" name="${fileName}" type="error">`,
    `[Error: ${error}]`,
    '</file>'
  ].join('\n');
}

/**
 * Check if file is a text file based on extension
 */
function isTextFileType(filePath: string): boolean {
  const ext = extname(filePath).toLowerCase();
  return TEXT_EXTENSIONS.includes(ext);
}

/**
 * Format file size for display
 */
function formatFileSize(bytes: number): string {
  if (bytes === 0) {return '0 B';}
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Extract file paths from text
 */
export function extractFilePaths(text: string): string[] {
  const fileTagPattern = /@([^\s@]+)/g;
  const matches = Array.from(text.matchAll(fileTagPattern));
  return matches.map(match => match[1]);
}

/**
 * Validate file paths and return status
 */
export function validateFilePaths(filePaths: string[], workingDir: string = process.cwd()): {
  valid: Array<{ path: string; size: number; type: 'text' | 'binary' }>;
  invalid: Array<{ path: string; error: string }>;
} {
  const valid: Array<{ path: string; size: number; type: 'text' | 'binary' }> = [];
  const invalid: Array<{ path: string; error: string }> = [];
  
  for (const filePath of filePaths) {
    try {
      const resolvedPath = resolve(workingDir, filePath);
      
      if (!existsSync(resolvedPath)) {
        invalid.push({ path: filePath, error: 'File not found' });
        continue;
      }
      
      const stats = statSync(resolvedPath);
      
      if (!stats.isFile()) {
        invalid.push({ path: filePath, error: 'Path is not a file' });
        continue;
      }
      
      if (stats.size > MAX_FILE_SIZE) {
        invalid.push({ 
          path: filePath, 
          error: `File too large (${formatFileSize(stats.size)}). Maximum: ${formatFileSize(MAX_FILE_SIZE)}` 
        });
        continue;
      }
      
      const type = isTextFileType(filePath) ? 'text' : 'binary';
      valid.push({ path: filePath, size: stats.size, type });
      
    } catch (error) {
      invalid.push({ 
        path: filePath, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
    }
  }
  
  return { valid, invalid };
}

/**
 * Get file information without reading content
 */
export function getFileInfo(filePath: string, workingDir: string = process.cwd()): {
  exists: boolean;
  isFile?: boolean;
  size?: number;
  type?: 'text' | 'binary';
  error?: string;
} {
  try {
    const resolvedPath = resolve(workingDir, filePath);
    
    if (!existsSync(resolvedPath)) {
      return { exists: false };
    }
    
    const stats = statSync(resolvedPath);
    
    if (!stats.isFile()) {
      return { exists: true, isFile: false, error: 'Path is not a file' };
    }
    
    const type = isTextFileType(filePath) ? 'text' : 'binary';
    
    return {
      exists: true,
      isFile: true,
      size: stats.size,
      type
    };
    
  } catch (error) {
    return {
      exists: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Preview file content (first few lines)
 */
export function previewFileContent(filePath: string, workingDir: string = process.cwd(), maxLines: number = 10): {
  success: boolean;
  preview?: string;
  totalLines?: number;
  error?: string;
} {
  try {
    const resolvedPath = resolve(workingDir, filePath);
    
    if (!existsSync(resolvedPath)) {
      return { success: false, error: 'File not found' };
    }
    
    const stats = statSync(resolvedPath);
    
    if (!stats.isFile()) {
      return { success: false, error: 'Path is not a file' };
    }
    
    if (!isTextFileType(filePath)) {
      return { 
        success: true, 
        preview: `[Binary file: ${basename(filePath)} (${formatFileSize(stats.size)})]`,
        totalLines: 1
      };
    }
    
    const content = readFileSync(resolvedPath, 'utf-8');
    const lines = content.split('\n');
    const preview = lines.slice(0, maxLines).join('\n');
    
    return {
      success: true,
      preview: preview + (lines.length > maxLines ? '\n...' : ''),
      totalLines: lines.length
    };
    
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
