import { existsSync, readFileSync } from 'fs';
import { join } from 'path';
import { logInfo, logError } from '../../logger/log.js';
import { loadConfig } from '../../config.js';
import { getSessionsDir } from '../../storage/save-rollout.js';
import { loadHistory } from '../../storage/command-history.js';
let currentSessionId = null;
let sessionStartTime = Date.now();
let messageCount = 0;
export async function getSessionContext() {
    try {
        logInfo('Gathering session context');
        const config = loadConfig();
        const sessionId = getCurrentSessionId();
        const context = {
            id: sessionId,
            startTime: sessionStartTime,
            duration: Date.now() - sessionStartTime,
            messageCount: messageCount,
            lastActivity: Date.now(),
            preferences: {
                model: config.model,
                provider: config.provider,
                approvalMode: config.approvalMode,
                verbosity: 'normal',
                theme: 'default'
            },
            history: await getHistoryContext(),
            interaction: await getInteractionContext(),
            current: await getCurrentContext(),
            user: await getUserContext()
        };
        logInfo('Session context gathered successfully', {
            sessionId: context.id,
            duration: context.duration,
            messageCount: context.messageCount
        });
        return context;
    }
    catch (error) {
        logError('Failed to gather session context', error instanceof Error ? error : new Error(String(error)));
        return getMinimalSessionContext();
    }
}
function getCurrentSessionId() {
    if (!currentSessionId) {
        currentSessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    return currentSessionId;
}
export function incrementMessageCount() {
    messageCount++;
}
export function resetSession() {
    currentSessionId = null;
    sessionStartTime = Date.now();
    messageCount = 0;
}
async function getHistoryContext() {
    const history = {
        commands: [],
        recentCommands: [],
        frequentCommands: [],
        commandCount: 0
    };
    try {
        const commandHistory = loadHistory();
        history.commands = commandHistory;
        history.commandCount = commandHistory.length;
        history.recentCommands = commandHistory
            .slice(-10)
            .map(entry => entry.command);
        const commandCounts = new Map();
        commandHistory.forEach(entry => {
            const count = commandCounts.get(entry.command) || 0;
            commandCounts.set(entry.command, count + 1);
        });
        history.frequentCommands = Array.from(commandCounts.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5)
            .map(([command]) => command);
    }
    catch (error) {
        logError('Failed to load command history', error instanceof Error ? error : new Error(String(error)));
    }
    return history;
}
async function getInteractionContext() {
    const interaction = {
        totalSessions: 0,
        averageSessionLength: 0,
        preferredCommands: [],
        errorRate: 0,
        successRate: 0
    };
    try {
        const sessionsDir = getSessionsDir();
        if (existsSync(sessionsDir)) {
            const { readdirSync } = await import('fs');
            const sessionFiles = readdirSync(sessionsDir).filter(f => f.endsWith('.json'));
            interaction.totalSessions = sessionFiles.length;
            const recentSessions = sessionFiles
                .slice(-10)
                .map(file => {
                try {
                    const sessionPath = join(sessionsDir, file);
                    const sessionData = JSON.parse(readFileSync(sessionPath, 'utf8'));
                    return sessionData;
                }
                catch {
                    return null;
                }
            })
                .filter(Boolean);
            if (recentSessions.length > 0) {
                const totalDuration = recentSessions.reduce((sum, session) => {
                    const start = session.metadata?.timestamp || 0;
                    const end = session.metadata?.lastActivity || start;
                    return sum + (end - start);
                }, 0);
                interaction.averageSessionLength = totalDuration / recentSessions.length;
                const commandHistory = loadHistory();
                const recentCommands = commandHistory.slice(-50);
                if (recentCommands.length > 0) {
                    const successfulCommands = recentCommands.filter(cmd => cmd.success !== false).length;
                    interaction.successRate = successfulCommands / recentCommands.length;
                    interaction.errorRate = 1 - interaction.successRate;
                }
            }
        }
    }
    catch (error) {
        logError('Failed to analyze interaction patterns', error instanceof Error ? error : new Error(String(error)));
    }
    return interaction;
}
async function getCurrentContext() {
    const current = {
        workingDirectory: process.cwd(),
        activeFiles: [],
        recentFiles: [],
        openProjects: []
    };
    try {
        const commandHistory = loadHistory();
        const fileCommands = commandHistory
            .filter(entry => {
            const cmd = entry.command.toLowerCase();
            return cmd.includes('cat') || cmd.includes('edit') || cmd.includes('open') ||
                cmd.includes('code') || cmd.includes('vim') || cmd.includes('nano');
        })
            .slice(-20);
        const filePattern = /(?:cat|edit|open|code|vim|nano)\s+([^\s]+)/gi;
        const recentFiles = new Set();
        fileCommands.forEach(entry => {
            let match;
            while ((match = filePattern.exec(entry.command)) !== null) {
                const filePath = match[1];
                if (filePath && !filePath.startsWith('-')) {
                    recentFiles.add(filePath);
                }
            }
        });
        current.recentFiles = Array.from(recentFiles).slice(0, 10);
        current.openProjects = [process.cwd()];
    }
    catch (error) {
        logError('Failed to get current context', error instanceof Error ? error : new Error(String(error)));
    }
    return current;
}
async function getUserContext() {
    const user = {
        expertise: [],
        preferences: {},
        customInstructions: undefined,
        learningMode: false
    };
    try {
        const config = loadConfig();
        user.preferences = {
            notifications: config.enableNotifications,
            logging: config.enableLogging,
            safetyLevel: 'moderate',
            autoApproval: config.approvalMode === 'full-auto'
        };
        const commandHistory = loadHistory();
        const commands = commandHistory.map(entry => entry.command.toLowerCase());
        const expertisePatterns = {
            'git': /git\s+/,
            'docker': /docker\s+/,
            'kubernetes': /kubectl\s+/,
            'nodejs': /npm\s+|node\s+|yarn\s+/,
            'python': /python\s+|pip\s+|conda\s+/,
            'rust': /cargo\s+|rustc\s+/,
            'go': /go\s+(run|build|test)/,
            'database': /mysql\s+|psql\s+|mongo\s+/,
            'aws': /aws\s+/,
            'linux': /sudo\s+|systemctl\s+|grep\s+|awk\s+/
        };
        for (const [area, pattern] of Object.entries(expertisePatterns)) {
            const matchCount = commands.filter(cmd => pattern.test(cmd)).length;
            if (matchCount > 5) {
                user.expertise.push(area);
            }
        }
        if (config.projectDocPath && existsSync(config.projectDocPath)) {
            try {
                user.customInstructions = readFileSync(config.projectDocPath, 'utf8');
            }
            catch {
            }
        }
        user.learningMode = commandHistory.length < 50;
    }
    catch (error) {
        logError('Failed to get user context', error instanceof Error ? error : new Error(String(error)));
    }
    return user;
}
function getMinimalSessionContext() {
    const sessionId = getCurrentSessionId();
    return {
        id: sessionId,
        startTime: sessionStartTime,
        duration: Date.now() - sessionStartTime,
        messageCount: messageCount,
        lastActivity: Date.now(),
        preferences: {
            model: 'gpt-4',
            provider: 'openai',
            approvalMode: 'suggest',
            verbosity: 'normal',
            theme: 'default'
        },
        history: {
            commands: [],
            recentCommands: [],
            frequentCommands: [],
            commandCount: 0
        },
        interaction: {
            totalSessions: 0,
            averageSessionLength: 0,
            preferredCommands: [],
            errorRate: 0,
            successRate: 0
        },
        current: {
            workingDirectory: process.cwd(),
            activeFiles: [],
            recentFiles: [],
            openProjects: []
        },
        user: {
            expertise: [],
            preferences: {},
            learningMode: true
        }
    };
}
export function updateLastActivity() {
}
export function getSessionStats() {
    return {
        id: getCurrentSessionId(),
        startTime: sessionStartTime,
        duration: Date.now() - sessionStartTime,
        messageCount: messageCount
    };
}
