/**
 * Git Diff Utilities
 * 
 * Provides comprehensive git diff functionality with syntax highlighting
 * Supports various diff formats and file filtering
 */

import { execSync } from 'child_process';
import { existsSync } from 'fs';
import chalk from 'chalk';
import { checkInGit } from './check-in-git.js';

export interface DiffOptions {
  cached?: boolean;
  files?: string[];
  context?: number;
  wordDiff?: boolean;
  noColor?: boolean;
  unified?: boolean;
}

export interface DiffResult {
  success: boolean;
  output: string;
  error?: string;
  files: string[];
  stats: {
    additions: number;
    deletions: number;
    files: number;
  };
}

/**
 * Get git diff with options
 */
export function getGitDiff(
  options: DiffOptions = {},
  workdir: string = process.cwd()
): DiffResult {
  if (!checkInGit(workdir)) {
    return {
      success: false,
      error: 'Not in a git repository',
      output: '',
      files: [],
      stats: { additions: 0, deletions: 0, files: 0 }
    };
  }

  try {
    // Build git diff command
    const args = ['diff'];
    
    if (options.cached) {
      args.push('--cached');
    }
    
    if (options.context !== undefined) {
      args.push(`--unified=${options.context}`);
    } else if (options.unified) {
      args.push('--unified');
    }
    
    if (options.wordDiff) {
      args.push('--word-diff');
    }
    
    if (options.noColor) {
      args.push('--no-color');
    } else {
      args.push('--color=always');
    }
    
    // Add file filters
    if (options.files && options.files.length > 0) {
      args.push('--');
      args.push(...options.files);
    }

    const command = `git ${args.join(' ')}`;
    const output = execSync(command, {
      cwd: workdir,
      encoding: 'utf-8',
      timeout: 30000
    });

    // Get diff stats
    const stats = getDiffStats(workdir, options);
    
    // Extract changed files
    const files = extractChangedFiles(output);

    return {
      success: true,
      output,
      files,
      stats
    };

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      output: '',
      files: [],
      stats: { additions: 0, deletions: 0, files: 0 }
    };
  }
}

/**
 * Get diff statistics
 */
export function getDiffStats(
  workdir: string = process.cwd(),
  options: DiffOptions = {}
): { additions: number; deletions: number; files: number } {
  try {
    const args = ['diff', '--stat'];
    
    if (options.cached) {
      args.push('--cached');
    }
    
    if (options.files && options.files.length > 0) {
      args.push('--');
      args.push(...options.files);
    }

    const command = `git ${args.join(' ')}`;
    const output = execSync(command, {
      cwd: workdir,
      encoding: 'utf-8',
      timeout: 10000
    });

    // Parse stats from output
    const lines = output.trim().split('\n');
    const lastLine = lines[lines.length - 1];
    
    if (lastLine.includes('file') && (lastLine.includes('insertion') || lastLine.includes('deletion'))) {
      const fileMatch = lastLine.match(/(\d+) files? changed/);
      const addMatch = lastLine.match(/(\d+) insertions?\(\+\)/);
      const delMatch = lastLine.match(/(\d+) deletions?\(-\)/);
      
      return {
        files: fileMatch ? parseInt(fileMatch[1]) : 0,
        additions: addMatch ? parseInt(addMatch[1]) : 0,
        deletions: delMatch ? parseInt(delMatch[1]) : 0
      };
    }

    return { additions: 0, deletions: 0, files: 0 };

  } catch {
    return { additions: 0, deletions: 0, files: 0 };
  }
}

/**
 * Extract changed files from diff output
 */
function extractChangedFiles(diffOutput: string): string[] {
  const files: string[] = [];
  const lines = diffOutput.split('\n');
  
  for (const line of lines) {
    if (line.startsWith('diff --git')) {
      const match = line.match(/diff --git a\/(.+) b\/(.+)/);
      if (match) {
        files.push(match[2]); // Use the "b/" version (after changes)
      }
    }
  }
  
  return [...new Set(files)]; // Remove duplicates
}

/**
 * Format diff output with syntax highlighting
 */
export function formatDiffOutput(diffOutput: string): string {
  const lines = diffOutput.split('\n');
  const formattedLines: string[] = [];

  for (const line of lines) {
    if (line.startsWith('+++') || line.startsWith('---')) {
      formattedLines.push(chalk.bold(line));
    } else if (line.startsWith('@@')) {
      formattedLines.push(chalk.cyan(line));
    } else if (line.startsWith('+')) {
      formattedLines.push(chalk.green(line));
    } else if (line.startsWith('-')) {
      formattedLines.push(chalk.red(line));
    } else if (line.startsWith('diff --git')) {
      formattedLines.push(chalk.yellow.bold(line));
    } else if (line.startsWith('index ')) {
      formattedLines.push(chalk.gray(line));
    } else {
      formattedLines.push(line);
    }
  }

  return formattedLines.join('\n');
}

/**
 * Get diff for specific file
 */
export function getFileDiff(
  filePath: string,
  options: DiffOptions = {},
  workdir: string = process.cwd()
): DiffResult {
  if (!existsSync(filePath)) {
    return {
      success: false,
      error: `File not found: ${filePath}`,
      output: '',
      files: [],
      stats: { additions: 0, deletions: 0, files: 0 }
    };
  }

  return getGitDiff({
    ...options,
    files: [filePath]
  }, workdir);
}

/**
 * Get diff between commits
 */
export function getCommitDiff(
  fromCommit: string,
  toCommit: string = 'HEAD',
  options: DiffOptions = {},
  workdir: string = process.cwd()
): DiffResult {
  if (!checkInGit(workdir)) {
    return {
      success: false,
      error: 'Not in a git repository',
      output: '',
      files: [],
      stats: { additions: 0, deletions: 0, files: 0 }
    };
  }

  try {
    const args = ['diff', fromCommit, toCommit];
    
    if (options.context !== undefined) {
      args.push(`--unified=${options.context}`);
    }
    
    if (options.wordDiff) {
      args.push('--word-diff');
    }
    
    if (!options.noColor) {
      args.push('--color=always');
    }
    
    if (options.files && options.files.length > 0) {
      args.push('--');
      args.push(...options.files);
    }

    const command = `git ${args.join(' ')}`;
    const output = execSync(command, {
      cwd: workdir,
      encoding: 'utf-8',
      timeout: 30000
    });

    const files = extractChangedFiles(output);
    const stats = { additions: 0, deletions: 0, files: files.length };

    return {
      success: true,
      output,
      files,
      stats
    };

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      output: '',
      files: [],
      stats: { additions: 0, deletions: 0, files: 0 }
    };
  }
}

/**
 * Check if there are any changes to diff
 */
export function hasChangesToDiff(
  options: DiffOptions = {},
  workdir: string = process.cwd()
): boolean {
  const result = getGitDiff(options, workdir);
  return result.success && result.output.trim().length > 0;
}

/**
 * Get summary of changes
 */
export function getDiffSummary(
  options: DiffOptions = {},
  workdir: string = process.cwd()
): {
  hasChanges: boolean;
  stats: { additions: number; deletions: number; files: number };
  files: string[];
} {
  const result = getGitDiff(options, workdir);
  
  return {
    hasChanges: result.success && result.output.trim().length > 0,
    stats: result.stats,
    files: result.files
  };
}

/**
 * Create diff patch file
 */
export function createPatchFile(
  outputPath: string,
  options: DiffOptions = {},
  workdir: string = process.cwd()
): boolean {
  try {
    const result = getGitDiff(options, workdir);
    
    if (!result.success || !result.output) {
      return false;
    }

    const fs = require('fs');
    fs.writeFileSync(outputPath, result.output);
    
    return true;
  } catch {
    return false;
  }
}
