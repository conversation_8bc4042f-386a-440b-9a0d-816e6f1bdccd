import { writeFileSync, readFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import { homedir } from 'os';
const DEFAULT_CONFIG = {
    maxSize: 1000,
    saveHistory: true,
    sensitivePatterns: [
        'api[_-]?key',
        'password',
        'secret',
        'token',
        'auth',
        'credential',
        'private[_-]?key'
    ]
};
const HISTORY_DIR = join(homedir(), '.kritrima-ai');
const HISTORY_FILE = join(HISTORY_DIR, 'command-history.json');
let historyCache = null;
let configCache = null;
function loadConfig() {
    if (configCache) {
        return configCache;
    }
    configCache = { ...DEFAULT_CONFIG };
    return configCache;
}
export function loadHistory() {
    if (historyCache) {
        return historyCache;
    }
    try {
        if (!existsSync(HISTORY_FILE)) {
            historyCache = [];
            return historyCache;
        }
        const content = readFileSync(HISTORY_FILE, 'utf-8');
        const data = JSON.parse(content);
        if (Array.isArray(data)) {
            historyCache = data.filter(entry => entry &&
                typeof entry.command === 'string' &&
                typeof entry.timestamp === 'number');
        }
        else {
            historyCache = [];
        }
        return historyCache;
    }
    catch (error) {
        console.warn('Warning: Could not load command history:', error);
        historyCache = [];
        return historyCache;
    }
}
function saveHistory() {
    try {
        const config = loadConfig();
        if (!config.saveHistory) {
            return;
        }
        if (!existsSync(HISTORY_DIR)) {
            mkdirSync(HISTORY_DIR, { recursive: true });
        }
        const history = loadHistory();
        writeFileSync(HISTORY_FILE, JSON.stringify(history, null, 2));
    }
    catch (error) {
        console.warn('Warning: Could not save command history:', error);
    }
}
function containsSensitiveData(command) {
    const config = loadConfig();
    const lowerCommand = command.toLowerCase();
    for (const pattern of config.sensitivePatterns) {
        const regex = new RegExp(pattern, 'i');
        if (regex.test(lowerCommand)) {
            return true;
        }
    }
    const sensitivePatterns = [
        /--?api[_-]?key\s*[=:]\s*\S+/i,
        /--?password\s*[=:]\s*\S+/i,
        /--?token\s*[=:]\s*\S+/i,
        /--?secret\s*[=:]\s*\S+/i,
        /export\s+\w*(?:key|password|token|secret)\w*\s*=/i,
        /set\s+\w*(?:key|password|token|secret)\w*\s*=/i
    ];
    for (const pattern of sensitivePatterns) {
        if (pattern.test(command)) {
            return true;
        }
    }
    return false;
}
export function addToHistory(command, success) {
    const config = loadConfig();
    if (!config.saveHistory) {
        return;
    }
    if (!command.trim()) {
        return;
    }
    if (containsSensitiveData(command)) {
        return;
    }
    const history = loadHistory();
    if (history.length > 0 && history[history.length - 1].command === command) {
        return;
    }
    const entry = {
        command: command.trim(),
        timestamp: Date.now(),
        success
    };
    history.push(entry);
    if (history.length > config.maxSize) {
        history.splice(0, history.length - config.maxSize);
    }
    saveHistory();
}
export function getHistory() {
    return [...loadHistory()];
}
export function getCommandHistory() {
    return getHistory();
}
export function searchHistory(query) {
    const history = loadHistory();
    const lowerQuery = query.toLowerCase();
    return history.filter(entry => entry.command.toLowerCase().includes(lowerQuery));
}
export function getRecentCommands(count = 10) {
    const history = loadHistory();
    return history.slice(-count).reverse();
}
export function getCommandAtIndex(index) {
    const history = loadHistory();
    if (index < 0 || index >= history.length) {
        return null;
    }
    return history[index].command;
}
export function getHistorySize() {
    return loadHistory().length;
}
export function clearHistory() {
    historyCache = [];
    saveHistory();
}
export function getHistoryStats() {
    const history = loadHistory();
    if (history.length === 0) {
        return {
            totalCommands: 0,
            uniqueCommands: 0,
            successfulCommands: 0,
            failedCommands: 0
        };
    }
    const uniqueCommands = new Set(history.map(entry => entry.command)).size;
    const successfulCommands = history.filter(entry => entry.success === true).length;
    const failedCommands = history.filter(entry => entry.success === false).length;
    const timestamps = history.map(entry => entry.timestamp);
    const oldestTimestamp = Math.min(...timestamps);
    const newestTimestamp = Math.max(...timestamps);
    return {
        totalCommands: history.length,
        uniqueCommands,
        successfulCommands,
        failedCommands,
        oldestCommand: new Date(oldestTimestamp),
        newestCommand: new Date(newestTimestamp)
    };
}
export function exportHistory(outputPath) {
    try {
        const history = loadHistory();
        writeFileSync(outputPath, JSON.stringify(history, null, 2));
        return true;
    }
    catch (error) {
        console.warn('Warning: Could not export history:', error);
        return false;
    }
}
export function importHistory(inputPath, merge = false) {
    try {
        if (!existsSync(inputPath)) {
            return false;
        }
        const content = readFileSync(inputPath, 'utf-8');
        const importedHistory = JSON.parse(content);
        if (!Array.isArray(importedHistory)) {
            return false;
        }
        const validEntries = importedHistory.filter(entry => entry &&
            typeof entry.command === 'string' &&
            typeof entry.timestamp === 'number');
        if (merge) {
            const existingHistory = loadHistory();
            const combinedHistory = [...existingHistory, ...validEntries];
            const uniqueHistory = Array.from(new Map(combinedHistory.map(entry => [entry.command + entry.timestamp, entry])).values()).sort((a, b) => a.timestamp - b.timestamp);
            historyCache = uniqueHistory;
        }
        else {
            historyCache = validEntries.sort((a, b) => a.timestamp - b.timestamp);
        }
        saveHistory();
        return true;
    }
    catch (error) {
        console.warn('Warning: Could not import history:', error);
        return false;
    }
}
export function updateConfig(newConfig) {
    const config = loadConfig();
    configCache = { ...config, ...newConfig };
    if (newConfig.maxSize !== undefined) {
        const history = loadHistory();
        if (history.length > newConfig.maxSize) {
            history.splice(0, history.length - newConfig.maxSize);
            saveHistory();
        }
    }
}
export class HistoryNavigator {
    currentIndex = -1;
    history;
    constructor() {
        this.history = loadHistory();
    }
    getPrevious() {
        if (this.history.length === 0) {
            return null;
        }
        if (this.currentIndex === -1) {
            this.currentIndex = this.history.length - 1;
        }
        else if (this.currentIndex > 0) {
            this.currentIndex--;
        }
        return this.history[this.currentIndex]?.command || null;
    }
    getNext() {
        if (this.history.length === 0 || this.currentIndex === -1) {
            return null;
        }
        if (this.currentIndex < this.history.length - 1) {
            this.currentIndex++;
            return this.history[this.currentIndex].command;
        }
        else {
            this.currentIndex = -1;
            return '';
        }
    }
    reset() {
        this.currentIndex = -1;
    }
    refresh() {
        this.history = loadHistory();
        this.currentIndex = -1;
    }
}
