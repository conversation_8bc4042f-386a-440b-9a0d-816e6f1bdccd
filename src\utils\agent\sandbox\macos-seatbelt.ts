/**
 * macOS Seatbelt Sandbox Implementation
 * 
 * Uses macOS Seatbelt for process sandboxing
 * Restricts network access, file system access, and system calls
 */

import { spawn } from 'child_process';
import { existsSync, writeFileSync, unlinkSync } from 'fs';
import { tmpdir } from 'os';
import * as path from 'path';
import { join } from 'path';
import { logInfo, logError } from '../../logger/log.js';
import type { ExecInput, ExecResult, AppConfig } from '../../../types/index.js';

/**
 * Execute command with Seatbelt sandboxing
 */
export async function execWithSeatbelt(
  input: ExecInput,
  _config: AppConfig
): Promise<ExecResult> {
  const startTime = Date.now();
  
  logInfo('Executing command with Seatbelt sandbox', {
    command: input.command,
    workdir: input.workdir,
    timeout: input.timeout
  });

  try {
    // Check if we're on macOS
    if (process.platform !== 'darwin') {
      throw new Error('Seatbelt is only available on macOS');
    }

    // Prepare sandbox profile
    const profilePath = createSeatbeltProfile(input, config);
    
    try {
      // Execute command in sandbox
      const result = await executeWithSeatbelt(input, profilePath, input.timeout || 30000);
      
      const duration = Date.now() - startTime;
      
      logInfo('Seatbelt sandboxed command execution completed', {
        command: input.command,
        exitCode: result.exitCode,
        duration,
        success: result.exitCode === 0
      });

      return {
        success: result.exitCode === 0,
        output: result.stdout + (result.stderr ? `\nSTDERR:\n${result.stderr}` : ''),
        error: result.stderr || undefined,
        exitCode: result.exitCode,
        duration,
        command: input.command,
        workdir: input.workdir || process.cwd()
      };

    } finally {
      // Cleanup sandbox profile
      if (existsSync(profilePath)) {
        unlinkSync(profilePath);
      }
    }

  } catch (error) {
    const duration = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    logError('Seatbelt sandboxed command execution failed', error instanceof Error ? error : new Error(errorMessage));

    return {
      success: false,
      output: '',
      error: errorMessage,
      exitCode: -1,
      duration,
      command: input.command,
      workdir: input.workdir || process.cwd()
    };
  }
}

/**
 * Create Seatbelt sandbox profile
 */
function createSeatbeltProfile(input: ExecInput, _config: AppConfig): string {
  const workdir = input.workdir ? path.resolve(input.workdir) : process.cwd();
  const profilePath = join(tmpdir(), `seatbelt-${Date.now()}-${Math.random().toString(36).substring(7)}.sb`);
  
  // Seatbelt profile configuration
  const profile = `
;; Seatbelt sandbox profile for Kritrima AI CLI
;; Restricts file system access, network access, and system calls

(version 1)

;; Deny everything by default
(deny default)

;; Allow basic system operations
(allow process-info* (target self))
(allow process-info-pidinfo (target self))
(allow process-info-pidfdinfo (target self))
(allow process-info-pidfileportinfo (target self))
(allow process-info-setcontrol (target self))
(allow process-exec (literal "/bin/sh") (literal "/bin/bash") (literal "/usr/bin/env"))

;; Allow reading system libraries and frameworks
(allow file-read*
    (subpath "/System/Library")
    (subpath "/usr/lib")
    (subpath "/usr/share")
    (subpath "/Library/Frameworks")
    (subpath "/Library/Application Support")
)

;; Allow reading common system files
(allow file-read*
    (literal "/etc/passwd")
    (literal "/etc/group")
    (literal "/etc/hosts")
    (literal "/etc/resolv.conf")
    (literal "/var/db/timezone/localtime")
    (literal "/usr/share/zoneinfo")
)

;; Allow reading and writing in working directory
(allow file-read* file-write*
    (subpath "${workdir}")
)

;; Allow reading and writing in temp directory
(allow file-read* file-write*
    (subpath "${tmpdir()}")
)

;; Allow reading user's home directory (read-only)
(allow file-read*
    (subpath (param "HOME_DIR"))
)

;; Allow basic I/O operations
(allow file-read* file-write*
    (literal "/dev/null")
    (literal "/dev/zero")
    (literal "/dev/urandom")
    (literal "/dev/stdin")
    (literal "/dev/stdout")
    (literal "/dev/stderr")
)

;; Allow process creation for common tools
(allow process-exec
    (literal "/bin/sh")
    (literal "/bin/bash")
    (literal "/bin/zsh")
    (literal "/usr/bin/env")
    (literal "/usr/bin/which")
    (literal "/usr/bin/whoami")
    (literal "/usr/bin/id")
    (literal "/usr/bin/uname")
    (literal "/bin/echo")
    (literal "/bin/cat")
    (literal "/bin/ls")
    (literal "/bin/pwd")
    (literal "/usr/bin/head")
    (literal "/usr/bin/tail")
    (literal "/usr/bin/grep")
    (literal "/usr/bin/sed")
    (literal "/usr/bin/awk")
    (literal "/usr/bin/sort")
    (literal "/usr/bin/uniq")
    (literal "/usr/bin/wc")
    (literal "/usr/bin/find")
    (literal "/usr/bin/xargs")
)

;; Allow common development tools
(allow process-exec
    (literal "/usr/bin/git")
    (literal "/usr/bin/node")
    (literal "/usr/bin/npm")
    (literal "/usr/bin/python3")
    (literal "/usr/bin/python")
    (literal "/usr/local/bin/node")
    (literal "/usr/local/bin/npm")
    (literal "/usr/local/bin/python3")
)

;; Allow signal operations
(allow signal (target self))

;; Allow memory operations
(allow vm-map)
(allow vm-protect)

;; Allow thread operations
(allow thread-create)
(allow thread-set-state)

;; Deny network access (can be enabled if needed)
(deny network*)

;; Deny dangerous operations
(deny file-write*
    (subpath "/System")
    (subpath "/usr")
    (subpath "/bin")
    (subpath "/sbin")
    (literal "/etc/passwd")
    (literal "/etc/group")
    (literal "/etc/hosts")
)

;; Deny process operations on other processes
(deny process-info* (target others))
(deny signal (target others))
`;

  writeFileSync(profilePath, profile);
  return profilePath;
}

/**
 * Execute command with Seatbelt
 */
function executeWithSeatbelt(
  input: ExecInput,
  profilePath: string,
  timeout: number
): Promise<{
  stdout: string;
  stderr: string;
  exitCode: number;
}> {
  return new Promise((promiseResolve, reject) => {
    const workdir = input.workdir ? path.resolve(input.workdir) : process.cwd();
    const [cmd, ...args] = input.command;
    
    // Use sandbox-exec to run command with Seatbelt profile
    const child = spawn('sandbox-exec', [
      '-f', profilePath,
      '-D', `HOME_DIR=${process.env.HOME || '/Users'}`,
      cmd,
      ...args
    ], {
      cwd: workdir,
      stdio: ['pipe', 'pipe', 'pipe'],
      timeout,
      env: {
        ...process.env,
        PATH: process.env.PATH || '/usr/bin:/bin:/usr/local/bin'
      }
    });

    let stdout = '';
    let stderr = '';

    // Collect stdout
    child.stdout?.on('data', (data) => {
      stdout += data.toString();
    });

    // Collect stderr
    child.stderr?.on('data', (data) => {
      stderr += data.toString();
    });

    // Handle process completion
    child.on('close', (code) => {
      promiseResolve({
        stdout: stdout.trim(),
        stderr: stderr.trim(),
        exitCode: code || 0
      });
    });

    // Handle process errors
    child.on('error', (error) => {
      reject(new Error(`Seatbelt sandbox process error: ${error.message}`));
    });

    // Handle timeout
    setTimeout(() => {
      if (!child.killed) {
        child.kill('SIGTERM');
        
        // Force kill after additional timeout
        setTimeout(() => {
          if (!child.killed) {
            child.kill('SIGKILL');
          }
        }, 5000);
        
        reject(new Error(`Seatbelt sandboxed command timed out after ${timeout}ms`));
      }
    }, timeout);
  });
}

/**
 * Test Seatbelt functionality
 */
export async function testSeatbelt(): Promise<{
  available: boolean;
  capabilities: string[];
  limitations: string[];
}> {
  const capabilities: string[] = [];
  const limitations: string[] = [];

  if (process.platform !== 'darwin') {
    return {
      available: false,
      capabilities: [],
      limitations: ['Seatbelt only available on macOS']
    };
  }

  try {
    // Check if sandbox-exec is available
    const { execSync } = require('child_process');
    
    try {
      execSync('which sandbox-exec', { stdio: 'ignore' });
      capabilities.push('Seatbelt sandbox available');
      capabilities.push('File system access control');
      capabilities.push('Process isolation');
      capabilities.push('Network access control');
      capabilities.push('System call filtering');
      
      return {
        available: true,
        capabilities,
        limitations: [
          'macOS specific',
          'Requires sandbox-exec tool',
          'Profile-based configuration'
        ]
      };
    } catch {
      limitations.push('sandbox-exec not found');
    }

  } catch {
    limitations.push('Failed to test Seatbelt capabilities');
  }

  return {
    available: false,
    capabilities,
    limitations
  };
}

/**
 * Get Seatbelt execution environment info
 */
export function getSeatbeltEnvironment(): {
  platform: string;
  sandboxing: boolean;
  restrictions: string[];
} {
  return {
    platform: 'darwin',
    sandboxing: true,
    restrictions: [
      'File system access control via Seatbelt profiles',
      'Network access denied by default',
      'Process execution limited to approved binaries',
      'System call filtering',
      'Memory and thread operation controls',
      'Signal operation restrictions'
    ]
  };
}
